{"version": 3, "file": "class-validator.modern.mjs", "sources": ["../src/class-validator.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { plainToClass } from 'class-transformer';\nimport { ValidationError, validate, validateSync } from 'class-validator';\nimport { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrors = (\n  errors: ValidationError[],\n  validateAllFieldCriteria: boolean,\n  parsedErrors: FieldErrors = {},\n  path = '',\n) => {\n  return errors.reduce((acc, error) => {\n    const _path = path ? `${path}.${error.property}` : error.property;\n\n    if (error.constraints) {\n      const key = Object.keys(error.constraints)[0];\n      acc[_path] = {\n        type: key,\n        message: error.constraints[key],\n      };\n\n      const _e = acc[_path];\n      if (validateAllFieldCriteria && _e) {\n        Object.assign(_e, { types: error.constraints });\n      }\n    }\n\n    if (error.children && error.children.length) {\n      parseErrors(error.children, validateAllFieldCriteria, acc, _path);\n    }\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const classValidatorResolver: Resolver =\n  (schema, schemaOptions = {}, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const { transformer, validator } = schemaOptions;\n    const data = plainToClass(schema, values, transformer);\n\n    const rawErrors = await (resolverOptions.mode === 'sync'\n      ? validateSync\n      : validate)(data, validator);\n\n    if (rawErrors.length) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrors(\n            rawErrors,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.rawValues ? values : data,\n      errors: {},\n    };\n  };\n"], "names": ["parseErrors", "errors", "validateAllFieldCriteria", "parsedErrors", "path", "reduce", "acc", "error", "_path", "property", "constraints", "key", "Object", "keys", "type", "message", "_e", "assign", "types", "children", "length", "classValidatorResolver", "schema", "schemaOptions", "resolverOptions", "async", "values", "_", "options", "transformer", "validator", "data", "plainToClass", "rawErrors", "mode", "validateSync", "validate", "toNestErrors", "shouldUseNativeValidation", "criteriaMode", "validateFieldsNatively", "rawValues"], "mappings": "6LAMA,MAAMA,EAAcA,CAClBC,EACAC,EACAC,EAA4B,CAAA,EAC5BC,EAAO,KAEAH,EAAOI,OAAO,CAACC,EAAKC,KACzB,MAAMC,EAAQJ,EAAO,GAAGA,KAAQG,EAAME,WAAaF,EAAME,SAEzD,GAAIF,EAAMG,YAAa,CACrB,MAAMC,EAAMC,OAAOC,KAAKN,EAAMG,aAAa,GAC3CJ,EAAIE,GAAS,CACXM,KAAMH,EACNI,QAASR,EAAMG,YAAYC,IAG7B,MAAMK,EAAKV,EAAIE,GACXN,GAA4Bc,GAC9BJ,OAAOK,OAAOD,EAAI,CAAEE,MAAOX,EAAMG,aAErC,CAMA,OAJIH,EAAMY,UAAYZ,EAAMY,SAASC,QACnCpB,EAAYO,EAAMY,SAAUjB,EAA0BI,EAAKE,GAGtDF,GACNH,GAGQkB,EACXA,CAACC,EAAQC,EAAgB,CAAA,EAAIC,EAAkB,CAAE,IACjDC,MAAOC,EAAQC,EAAGC,KAChB,MAAMC,YAAEA,EAAWC,UAAEA,GAAcP,EAC7BQ,EAAOC,EAAaV,EAAQI,EAAQG,GAEpCI,QAA4C,SAAzBT,EAAgBU,KACrCC,EACAC,GAAUL,EAAMD,GAEpB,OAAIG,EAAUb,OACL,CACLM,OAAQ,CAAE,EACVzB,OAAQoC,EACNrC,EACEiC,GACCL,EAAQU,2BACkB,QAAzBV,EAAQW,cAEZX,KAKNA,EAAQU,2BAA6BE,EAAuB,CAAA,EAAIZ,GAEzD,CACLF,OAAQF,EAAgBiB,UAAYf,EAASK,EAC7C9B,OAAQ"}