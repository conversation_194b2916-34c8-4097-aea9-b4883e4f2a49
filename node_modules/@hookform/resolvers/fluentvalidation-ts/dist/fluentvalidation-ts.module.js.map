{"version": 3, "file": "fluentvalidation-ts.module.js", "sources": ["../src/fluentvalidation-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  AsyncValidator,\n  ValidationErrors,\n  Validator,\n} from 'fluentvalidation-ts';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction traverseObject<T>(\n  object: ValidationErrors<T>,\n  errors: Record<string, FieldError>,\n  parentIndices: (string | number)[] = [],\n) {\n  for (const key in object) {\n    const currentIndex = [...parentIndices, key];\n    const currentValue = object[key];\n\n    if (Array.isArray(currentValue)) {\n      currentValue.forEach((item: any, index: number) => {\n        traverseObject(item, errors, [...currentIndex, index]);\n      });\n    } else if (typeof currentValue === 'object' && currentValue !== null) {\n      traverseObject(currentValue, errors, currentIndex);\n    } else if (typeof currentValue === 'string') {\n      errors[currentIndex.join('.')] = {\n        type: 'validation',\n        message: currentValue,\n      };\n    }\n  }\n}\n\nconst parseErrorSchema = <T>(\n  validationErrors: ValidationErrors<T>,\n  validateAllFieldCriteria: boolean,\n) => {\n  if (validateAllFieldCriteria) {\n    // TODO: check this but i think its always one validation error\n  }\n\n  const errors: Record<string, FieldError> = {};\n  traverseObject(validationErrors, errors);\n\n  return errors;\n};\n\nexport function fluentValidationResolver<TFieldValues extends FieldValues>(\n  validator: Validator<TFieldValues>,\n): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = validator.validate(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n\nexport function fluentAsyncValidationResolver<\n  TFieldValues extends FieldValues,\n  TValidator extends AsyncValidator<TFieldValues>,\n>(validator: TValidator): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = await validator.validateAsync(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n"], "names": ["traverseObject", "object", "errors", "parentIndices", "_loop", "currentIndex", "concat", "key", "currentValue", "Array", "isArray", "for<PERSON>ach", "item", "index", "join", "type", "message", "parseErrorSchema", "validationErrors", "validateAllFieldCriteria", "fluentValidationResolver", "validator", "values", "_context", "options", "validationResult", "validate", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "shouldUseNativeValidation", "validateFieldsNatively", "Promise", "resolve", "toNestErrors", "e", "reject", "fluentAsyncValidationResolver", "validateAsync", "then"], "mappings": "+EAQA,SAASA,EACPC,EACAC,EACAC,YAAAA,IAAAA,EAAqC,IAAE,IAAAC,EAAAA,WAGrC,IAAMC,EAAY,GAAAC,OAAOH,EAAa,CAAEI,IAClCC,EAAeP,EAAOM,GAExBE,MAAMC,QAAQF,GAChBA,EAAaG,QAAQ,SAACC,EAAWC,GAC/Bb,EAAeY,EAAMV,EAAM,GAAAI,OAAMD,EAAcQ,CAAAA,IACjD,GACiC,iBAAjBL,GAA8C,OAAjBA,EAC7CR,EAAeQ,EAAcN,EAAQG,GACJ,iBAAjBG,IAChBN,EAAOG,EAAaS,KAAK,MAAQ,CAC/BC,KAAM,aACNC,QAASR,GAGf,EAhBA,IAAK,IAAMD,KAAON,EAAMG,GAiB1B,CAEA,IAAMa,EAAmB,SACvBC,EACAC,GAMA,IAAMjB,EAAqC,GAG3C,OAFAF,EAAekB,EAAkBhB,GAE1BA,CACT,EAEgB,SAAAkB,EACdC,GAEA,OAAcC,SAAAA,EAAQC,EAAUC,GAAW,IACzC,IAAMC,EAAmBJ,EAAUK,SAASJ,GACtCK,EAAmD,IAAzCC,OAAOC,KAAKJ,GAAkBK,OAI9C,OAFAN,EAAQO,2BAA6BC,EAAuB,CAAE,EAAER,GAEhES,QAAAC,QAAOP,EACH,CACEL,OAAQA,EACRpB,OAAQ,CAAA,GAEV,CACEoB,OAAQ,CAAA,EACRpB,OAAQiC,EACNlB,EACEQ,GAIFD,IAGV,CAAC,MAAAY,GAAA,OAAAH,QAAAI,OAAAD,EACH,CAAA,CAAA,CAEgB,SAAAE,EAGdjB,GACA,OAAcC,SAAAA,EAAQC,EAAUC,GAAW,IAAA,OAAAS,QAAAC,QACVb,EAAUkB,cAAcjB,IAAOkB,KAAA,SAAxDf,GACN,IAAME,EAAmD,IAAzCC,OAAOC,KAAKJ,GAAkBK,OAI9C,OAFAN,EAAQO,2BAA6BC,EAAuB,CAAA,EAAIR,GAEzDG,EACH,CACEL,OAAQA,EACRpB,OAAQ,CACT,GACD,CACEoB,OAAQ,CAAA,EACRpB,OAAQiC,EACNlB,EACEQ,GAIFD,GAEF,EACR,CAAC,MAAAY,GAAA,OAAAH,QAAAI,OAAAD,EACH,CAAA,CAAA"}