{"hash": "201c798b", "configHash": "596248e8", "lockfileHash": "66052908", "browserHash": "e7b4a04e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "298a3b3c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "011f839b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "cac1f4a7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ac4ebd0c", "needsInterop": true}, "@capacitor/camera": {"src": "../../@capacitor/camera/dist/esm/index.js", "file": "@capacitor_camera.js", "fileHash": "745aab53", "needsInterop": false}, "@capacitor/device": {"src": "../../@capacitor/device/dist/esm/index.js", "file": "@capacitor_device.js", "fileHash": "edb7af59", "needsInterop": false}, "@capacitor/geolocation": {"src": "../../@capacitor/geolocation/dist/esm/index.js", "file": "@capacitor_geolocation.js", "fileHash": "024cc6c1", "needsInterop": false}, "@capacitor/haptics": {"src": "../../@capacitor/haptics/dist/esm/index.js", "file": "@capacitor_haptics.js", "fileHash": "b03a7fcf", "needsInterop": false}, "@capacitor/local-notifications": {"src": "../../@capacitor/local-notifications/dist/esm/index.js", "file": "@capacitor_local-notifications.js", "fileHash": "8cadee9e", "needsInterop": false}, "@capacitor/network": {"src": "../../@capacitor/network/dist/esm/index.js", "file": "@capacitor_network.js", "fileHash": "f2531a19", "needsInterop": false}, "@capacitor/push-notifications": {"src": "../../@capacitor/push-notifications/dist/esm/index.js", "file": "@capacitor_push-notifications.js", "fileHash": "31bc8db9", "needsInterop": false}, "@capacitor/status-bar": {"src": "../../@capacitor/status-bar/dist/esm/index.js", "file": "@capacitor_status-bar.js", "fileHash": "52e4d447", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "4686b6e1", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "f246c15d", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "e3571e1b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "c9b728b0", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "1856f42e", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "78ff0b17", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "9fa28597", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "0bea5367", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "8106d7e8", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "9cc0ddda", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "49d6da2f", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "095257a4", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "1b7d157b", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "02aa2413", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "89942a52", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "b0b97fdc", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d2dc0053", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7216c9c2", "needsInterop": true}, "react-leaflet": {"src": "../../react-leaflet/lib/index.js", "file": "react-leaflet.js", "fileHash": "c8e75f6b", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "add33484", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "12487ea7", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "9b7b8ed9", "needsInterop": false}}, "chunks": {"web-DJKZM54J": {"file": "web-DJKZM54J.js"}, "web-7WDZ5VTT": {"file": "web-7WDZ5VTT.js"}, "web-ELBWS2DR": {"file": "web-ELBWS2DR.js"}, "web-6QXAY6VZ": {"file": "web-6QXAY6VZ.js"}, "web-RWNTJFQJ": {"file": "web-RWNTJFQJ.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-JQURRHX6": {"file": "chunk-JQURRHX6.js"}, "chunk-G6WN2CYI": {"file": "chunk-G6WN2CYI.js"}, "chunk-43G6IMPT": {"file": "chunk-43G6IMPT.js"}, "chunk-MWGJIRSI": {"file": "chunk-MWGJIRSI.js"}, "chunk-4RO2SXZU": {"file": "chunk-4RO2SXZU.js"}, "chunk-W2OT6ZD3": {"file": "chunk-W2OT6ZD3.js"}, "chunk-FUFMHE3S": {"file": "chunk-FUFMHE3S.js"}, "chunk-USGN5C6Q": {"file": "chunk-USGN5C6Q.js"}, "chunk-BCQR5QVC": {"file": "chunk-BCQR5QVC.js"}, "chunk-YYW6Y33B": {"file": "chunk-YYW6Y33B.js"}, "chunk-5R5FMENF": {"file": "chunk-5R5FMENF.js"}, "chunk-KCFVMCIE": {"file": "chunk-KCFVMCIE.js"}, "chunk-DWM4GXJY": {"file": "chunk-DWM4GXJY.js"}, "chunk-G7KMZA27": {"file": "chunk-G7KMZA27.js"}, "chunk-OXZDJRWN": {"file": "chunk-OXZDJRWN.js"}, "chunk-PY4CUOXA": {"file": "chunk-PY4CUOXA.js"}, "chunk-E7TSFT4J": {"file": "chunk-E7TSFT4J.js"}, "chunk-SXRIVT2P": {"file": "chunk-SXRIVT2P.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-UKWEPBII": {"file": "chunk-UKWEPBII.js"}, "chunk-HAUB5OVN": {"file": "chunk-HAUB5OVN.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}