{"name": "@serialport/parser-packet-length", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "version": "13.0.0", "engines": {"node": ">=8.6.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "devDependencies": {"typescript": "5.2.2"}, "gitHead": "701ffd844f0950164da4dda4dca57e94891177a9"}