{"name": "@serialport/bindings-cpp", "description": "SerialPort Hardware bindings for node serialport written in c++", "version": "13.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "keywords": ["serialport-binding", "COM", "com port", "hardware", "iot", "modem", "serial port", "serial", "serialport", "tty", "UART"], "dependencies": {"@serialport/bindings-interface": "1.2.2", "@serialport/parser-readline": "12.0.0", "debug": "4.4.0", "node-addon-api": "8.3.0", "node-gyp-build": "4.8.4"}, "devDependencies": {"@semantic-release/exec": "6.0.3", "@serialport/binding-mock": "10.2.2", "@types/chai": "5.0.1", "@types/chai-subset": "1.3.5", "@types/debug": "4.1.12", "@types/mocha": "10.0.10", "@types/node": "22.8.2", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "cc": "3.0.1", "chai": "5.1.2", "chai-subset": "1.6.0", "esbuild": "0.24.2", "esbuild-register": "3.6.0", "eslint": "8.57.1", "mocha": "11.0.1", "node-abi": "3.71.0", "node-gyp": "11.0.0", "nyc": "17.1.0", "prebuildify": "6.0.1", "prebuildify-cross": "5.1.1", "semantic-release": "24.2.0", "shx": "0.3.4", "sinon": "19.0.2", "typescript": "5.7.2"}, "engines": {"node": ">=18.0.0"}, "scripts": {"build": "rm -rf dist && tsc -p tsconfig-build.json", "install": "node-gyp-build", "prebuildify": "prebuildify --napi --target 20.0.0 --force --strip --verbose", "prebuildify-cross": "prebuildify-cross --napi --target 20.0.0 --force --strip --verbose", "rebuild": "node-gyp rebuild", "format": "eslint lib test bin --fix", "lint": "eslint lib test bin && cc --verbose", "test": "nyc --reporter lcov --reporter text mocha", "test:arduino": "TEST_PORT=$(./bin/find-arduino.ts) npm test", "test:watch": "mocha -w", "semantic-release": "semantic-release", "typecheck": "tsc"}, "publishConfig": {"access": "public"}, "license": "MIT", "gypfile": true, "cc": {"filter": ["legal/copyright", "build/include"], "files": ["src/*.cpp", "src/*.h"], "linelength": "120"}, "binary": {"napi_versions": [8]}, "repository": {"type": "git", "url": "https://github.com/serialport/bindings-cpp.git"}, "funding": "https://opencollective.com/serialport/donate", "changelog": {"labels": {"breaking": ":boom: BREAKING CHANGES :boom:", "feature-request": "Features", "bug": "Bug Fixes", "docs": "Documentation", "internal": "Chores"}}, "mocha": {"bail": true, "require": ["esbuild-register"], "spec": "lib/**/*.test.*"}}