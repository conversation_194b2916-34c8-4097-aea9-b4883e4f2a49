#!/usr/bin/env node

/**
 * Database Initialization Script
 * This script initializes the SQLite database with proper schema and sample data
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const databasePath = path.join(__dirname, '../data/smartlocker.db');

// Ensure the data directory exists
const dataDir = path.dirname(databasePath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  console.log('Created data directory:', dataDir);
}

// Remove existing database to start fresh
if (fs.existsSync(databasePath)) {
  fs.unlinkSync(databasePath);
  console.log('Removed existing database');
}

const db = new Database(databasePath);

console.log('Initializing SQLite database...');

try {
  // Create all tables
  const createTablesSQL = `
    -- Users table
    CREATE TABLE users (
      id TEXT PRIMARY KEY NOT NULL,
      email TEXT NOT NULL UNIQUE,
      first_name TEXT,
      last_name TEXT,
      profile_image_url TEXT,
      role TEXT NOT NULL DEFAULT 'user',
      status TEXT NOT NULL DEFAULT 'active',
      phone_number TEXT,
      username TEXT UNIQUE,
      password TEXT,
      verification_status INTEGER DEFAULT 0,
      subscription_type TEXT DEFAULT 'basic',
      subscription_status TEXT DEFAULT 'active',
      subscription_start_date INTEGER,
      subscription_end_date INTEGER,
      trial_end_date INTEGER,
      payment_method TEXT,
      stripe_customer_id TEXT,
      stripe_subscription_id TEXT,
      restriction_reason TEXT,
      restricted_at INTEGER,
      restricted_by TEXT,
      last_login_at INTEGER,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Locations table
    CREATE TABLE locations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      address TEXT NOT NULL,
      city TEXT,
      state TEXT,
      zip_code TEXT,
      country TEXT DEFAULT 'US',
      latitude REAL,
      longitude REAL,
      is_active INTEGER DEFAULT 1,
      capacity INTEGER,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Lockers table
    CREATE TABLE lockers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      size TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'available',
      location_id INTEGER REFERENCES locations(id),
      esp32_id TEXT,
      last_maintenance_date INTEGER,
      maintenance_interval INTEGER,
      temperature_sensor INTEGER DEFAULT 0,
      humidity_sensor INTEGER DEFAULT 0,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Bookings table
    CREATE TABLE bookings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL REFERENCES users(id),
      locker_id INTEGER NOT NULL REFERENCES lockers(id),
      agent_id TEXT REFERENCES users(id),
      status TEXT NOT NULL DEFAULT 'pending',
      start_time INTEGER NOT NULL,
      end_time INTEGER,
      duration INTEGER,
      total_cost REAL,
      payment_status TEXT NOT NULL DEFAULT 'simulated_paid',
      payment_method TEXT DEFAULT 'demo_card',
      payment_id TEXT,
      notes TEXT,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Tasks table
    CREATE TABLE tasks (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      agent_id TEXT NOT NULL REFERENCES users(id),
      booking_id INTEGER REFERENCES bookings(id),
      type TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'pending',
      priority TEXT DEFAULT 'medium',
      estimated_time INTEGER,
      actual_time INTEGER,
      start_time INTEGER,
      completion_time INTEGER,
      earnings REAL,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Activities table
    CREATE TABLE activities (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      type TEXT NOT NULL,
      message TEXT NOT NULL,
      locker_id INTEGER REFERENCES lockers(id),
      user_id TEXT REFERENCES users(id),
      booking_id INTEGER REFERENCES bookings(id),
      task_id INTEGER REFERENCES tasks(id),
      severity TEXT DEFAULT 'info',
      metadata TEXT,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Chat messages table
    CREATE TABLE chat_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      from_user_id TEXT NOT NULL REFERENCES users(id),
      to_user_id TEXT NOT NULL REFERENCES users(id),
      booking_id INTEGER REFERENCES bookings(id),
      message TEXT NOT NULL,
      is_read INTEGER DEFAULT 0,
      read_at INTEGER,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Subscription plans table
    CREATE TABLE subscription_plans (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      display_name TEXT NOT NULL,
      description TEXT,
      price REAL NOT NULL,
      yearly_price REAL,
      currency TEXT NOT NULL DEFAULT 'USD',
      features TEXT NOT NULL,
      max_bookings_per_month INTEGER,
      max_concurrent_bookings INTEGER,
      priority_support INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Subscription history table
    CREATE TABLE subscription_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL REFERENCES users(id),
      plan_id INTEGER REFERENCES subscription_plans(id),
      action TEXT NOT NULL,
      from_plan TEXT,
      to_plan TEXT,
      amount REAL,
      currency TEXT DEFAULT 'USD',
      payment_method TEXT,
      transaction_id TEXT,
      reason TEXT,
      metadata TEXT,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- System Settings table for global configuration
    CREATE TABLE system_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT NOT NULL UNIQUE,
      value TEXT NOT NULL,
      type TEXT NOT NULL DEFAULT 'string',
      category TEXT NOT NULL,
      description TEXT,
      is_editable INTEGER DEFAULT 1,
      created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
      updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
    );

    -- Create indexes for better performance
    CREATE INDEX idx_locker_status ON lockers(status);
    CREATE INDEX idx_locker_location ON lockers(location_id);
    CREATE INDEX idx_booking_user ON bookings(user_id);
    CREATE INDEX idx_booking_locker ON bookings(locker_id);
    CREATE INDEX idx_booking_status ON bookings(status);
    CREATE INDEX idx_activity_type ON activities(type);
    CREATE INDEX idx_activity_created ON activities(created_at);
    CREATE INDEX idx_subscription_history_user ON subscription_history(user_id);
    CREATE INDEX idx_subscription_history_plan ON subscription_history(plan_id);
    CREATE INDEX idx_subscription_history_action ON subscription_history(action);
    CREATE INDEX idx_settings_key ON system_settings(key);
    CREATE INDEX idx_settings_category ON system_settings(category);
  `;

  db.exec(createTablesSQL);
  console.log('✅ Database schema created successfully');

  // Insert essential system data and default subscription plans
  const insertSystemData = `
    -- System initialization activity
    INSERT INTO activities (type, message, severity) VALUES
    ('system', 'Database initialized for real-time data', 'info');

    -- Default subscription plans
    INSERT INTO subscription_plans (name, display_name, description, price, yearly_price, features, max_bookings_per_month, max_concurrent_bookings, priority_support) VALUES
    ('basic', 'Basic Plan', 'Perfect for occasional users', 0.00, 0.00, '["Basic locker access", "Standard support", "Mobile app access"]', 10, 2, 0),
    ('premium', 'Premium Plan', 'Great for regular users', 9.99, 99.99, '["Unlimited locker access", "Priority support", "Advanced booking features", "Extended rental periods"]', 50, 5, 1),
    ('premium_plus', 'Premium Plus', 'Best for power users', 19.99, 199.99, '["Everything in Premium", "24/7 priority support", "Custom booking preferences", "Analytics dashboard", "API access"]', -1, 10, 1);
  `;

  db.exec(insertSystemData);
  console.log('✅ System initialization completed');

  // Verify the empty database structure
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  const lockerCount = db.prepare('SELECT COUNT(*) as count FROM lockers').get();
  const locationCount = db.prepare('SELECT COUNT(*) as count FROM locations').get();
  const activityCount = db.prepare('SELECT COUNT(*) as count FROM activities').get();

  console.log('\n📊 Empty Database Statistics:');
  console.log(`- Users: ${userCount.count} (ready for real users)`);
  console.log(`- Lockers: ${lockerCount.count} (ready for real lockers)`);
  console.log(`- Locations: ${locationCount.count} (ready for real locations)`);
  console.log(`- Activities: ${activityCount.count} (system log only)`);

  console.log('\n🎉 Empty database ready for real-time data!');
  console.log(`Database file: ${databasePath}`);
  console.log('\n💡 Next steps:');
  console.log('- Add real locations via admin panel');
  console.log('- Register real lockers with ESP32 devices');
  console.log('- Users can register and start using the system');

} catch (error) {
  console.error('❌ Error initializing database:', error);
  process.exit(1);
} finally {
  db.close();
}
