#!/usr/bin/env node

import { db } from '../server/db.js';
import { users } from '../shared/schema.js';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import chalk from 'chalk';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function createCreatorUser() {
  console.log(chalk.cyan('🔧 SwiSto Creator Account Setup'));
  console.log(chalk.gray('=' .repeat(40)));
  console.log();

  try {
    // Check if a creator already exists
    const existingCreator = await db.select()
      .from(users)
      .where(eq(users.role, 'creator'))
      .limit(1);

    if (existingCreator.length > 0) {
      console.log(chalk.yellow('⚠️  A creator account already exists:'));
      console.log(chalk.white(`   Email: ${existingCreator[0].email}`));
      console.log(chalk.white(`   Name: ${existingCreator[0].firstName} ${existingCreator[0].lastName}`));
      console.log();
      
      const overwrite = await question(chalk.cyan('Do you want to create another creator account? (y/N): '));
      if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
        console.log(chalk.gray('Operation cancelled.'));
        rl.close();
        return;
      }
      console.log();
    }

    // Collect user information
    console.log(chalk.cyan('Please provide the creator account details:'));
    console.log();

    const email = await question(chalk.white('Email: '));
    if (!email || !email.includes('@')) {
      console.log(chalk.red('❌ Invalid email address'));
      rl.close();
      return;
    }

    // Check if email already exists
    const existingUser = await db.select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser.length > 0) {
      console.log(chalk.red('❌ A user with this email already exists'));
      rl.close();
      return;
    }

    const firstName = await question(chalk.white('First Name: '));
    const lastName = await question(chalk.white('Last Name: '));
    const username = await question(chalk.white('Username: '));
    
    if (!username) {
      console.log(chalk.red('❌ Username is required'));
      rl.close();
      return;
    }

    // Check if username already exists
    const existingUsername = await db.select()
      .from(users)
      .where(eq(users.username, username))
      .limit(1);

    if (existingUsername.length > 0) {
      console.log(chalk.red('❌ A user with this username already exists'));
      rl.close();
      return;
    }

    const password = await question(chalk.white('Password (min 8 characters): '));
    if (!password || password.length < 8) {
      console.log(chalk.red('❌ Password must be at least 8 characters long'));
      rl.close();
      return;
    }

    console.log();
    console.log(chalk.yellow('Creating creator account...'));

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create the creator user
    const newCreator = await db.insert(users).values({
      id: randomUUID(),
      email,
      firstName,
      lastName,
      username,
      password: hashedPassword,
      role: 'creator',
      status: 'active',
      verificationStatus: true,
      subscriptionType: 'premium_plus',
      subscriptionStatus: 'active',
      subscriptionStartDate: Date.now(),
      subscriptionEndDate: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year
      lastLoginAt: Date.now(),
    }).returning();

    console.log();
    console.log(chalk.green('✅ Creator account created successfully!'));
    console.log(chalk.gray('=' .repeat(40)));
    console.log();
    console.log(chalk.cyan('Account Details:'));
    console.log(chalk.white(`   Email: ${email}`));
    console.log(chalk.white(`   Username: ${username}`));
    console.log(chalk.white(`   Name: ${firstName} ${lastName}`));
    console.log(chalk.white(`   Role: Creator`));
    console.log();
    console.log(chalk.yellow('🔑 The creator can now:'));
    console.log(chalk.white('   • Access all admin features'));
    console.log(chalk.white('   • Control feature visibility for other admins'));
    console.log(chalk.white('   • Manage system settings'));
    console.log(chalk.white('   • Enable/disable admin features'));
    console.log();
    console.log(chalk.cyan('💡 Login at: http://localhost:5000/admin'));

  } catch (error) {
    console.error(chalk.red('❌ Error creating creator account:'), error.message);
  } finally {
    rl.close();
  }
}

// Run the script
createCreatorUser().catch(console.error);
