import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import os from "os";

// ESP32 Integration (conditional import)
let ESP32Integration: any = null;
let ESP32USBIntegration: any = null;
let esp32Instance: any = null;

// ESP32 configuration
const enableESP32 = process.env.ENABLE_ESP32 === 'true';
const esp32Mode = process.env.ESP32_MODE || 'wifi'; // 'wifi' or 'usb'

// ESP32 integration will be loaded inside the async function

// Security imports
import {
  helmetConfig,
  corsConfig,
  sessionConfig,
  rateLimiters,
  compressionConfig,
  sanitizeRequest,
  securityHeaders,
  SECURITY_CONFIG
} from "./security/config";
import {
  securityMonitoring,
  rateLimitMonitoring,
  anomalyDetection
} from "./security/monitoring";
import { sanitizeUserInput } from "./security/auth";

// Function to get local network IP addresses
function getNetworkAddresses() {
  const interfaces = os.networkInterfaces();
  const addresses: string[] = [];

  for (const name of Object.keys(interfaces)) {
    const nets = interfaces[name];
    if (!nets) continue;

    for (const net of nets) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        addresses.push(net.address);
      }
    }
  }

  return addresses;
}

const app = express();

// Security Configuration
console.log('🔒 Initializing security middleware...');

if (process.env.NODE_ENV === 'production') {
  // Full security stack for production
  app.set('trust proxy', 1);
  app.use(compressionConfig);
  app.use(helmetConfig);
  app.use(securityHeaders);
  app.use(corsConfig);
  app.use(sessionConfig);
  app.use('/api/auth', rateLimiters.auth);
  app.use('/api', rateLimiters.api);
  app.use(rateLimiters.general);
  app.use(sanitizeRequest);
  app.use(sanitizeUserInput);
  app.use(securityMonitoring);
  app.use(rateLimitMonitoring);
  app.use(anomalyDetection);
} else {
  // Minimal security for development - avoid SSL/HTTPS issues
  console.log('🚧 Development mode: Using minimal security configuration');

  // Basic CORS for development - very permissive
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-User-ID');
    res.header('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Basic session for development (no secure cookies)
  app.use(sessionConfig);
}

// Request parsing with size limits (always needed)
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    // Store raw body for webhook verification if needed
    (req as any).rawBody = buf;
  }
}));
app.use(express.urlencoded({
  extended: false,
  limit: '10mb'
}));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Run database migrations first
  log('🗄️  Running database migrations...');
  try {
    const { MigrationRunner } = await import("./scripts/migrate.js");
    const dbPath = process.env.DATABASE_URL || "./database.sqlite";
    const migrationRunner = new MigrationRunner(dbPath);
    await migrationRunner.runMigrations();
    migrationRunner.close();
    log('✅ Database migrations completed successfully');
  } catch (error) {
    log('❌ Database migration failed:', error instanceof Error ? error.message : String(error));
    log('🚨 Server startup aborted due to migration failure');
    process.exit(1);
  }

  // Initialize feature flags
  log('🎛️  Initializing feature flags...');
  try {
    const { initializeFeatureFlags } = await import("./routes/admin/system-settings.js");
    await initializeFeatureFlags();
    log('✅ Feature flags initialized successfully');
  } catch (error) {
    log('⚠️  Feature flag initialization failed:', error instanceof Error ? error.message : String(error));
    log('🔄 Continuing without feature flag initialization...');
  }

  // Load ESP32 integration modules
  if (enableESP32) {
    try {
      if (esp32Mode === 'usb') {
        const module = await import('./esp32-usb-integration.js');
        ESP32USBIntegration = module.default || module;
        log('🔧 ESP32 USB integration module loaded');
      } else {
        const module = await import('./esp32-integration.js');
        ESP32Integration = module.default || module;
        log('🔧 ESP32 WiFi integration module loaded');
      }
    } catch (error) {
      log('⚠️  ESP32 integration module not found - continuing without hardware support');
      log(`💡 To enable ESP32 features, ensure server/esp32-${esp32Mode}-integration.js is present`);
      log(`📁 Looking for: ./esp32-${esp32Mode}-integration.js`);
      log(`🔍 Error: ${error.message}`);
    }
  }

  // Initialize ESP32 integration if enabled
  if (enableESP32 && (ESP32Integration || ESP32USBIntegration)) {
    try {
      if (esp32Mode === 'usb' && ESP32USBIntegration) {
        esp32Instance = new ESP32USBIntegration({
          baudRate: parseInt(process.env.ESP32_BAUD_RATE || '115200'),
          autoDetect: process.env.ESP32_AUTO_DETECT !== 'false'
        });
        log('🔧 ESP32 USB integration initialized');
        log('📡 Ready for ESP32 USB locker connections');
      } else if (ESP32Integration) {
        esp32Instance = new ESP32Integration(3001);
        log('🔧 ESP32 WebSocket server initialized on port 3001');
        log('📡 Ready for ESP32 WiFi locker connections');
      }

      // Set up ESP32 routes if available
      try {
        const { router: esp32Router, setESP32Integration } = require('./routes/esp32.js');
        setESP32Integration(esp32Instance);
        app.use('/api/esp32', esp32Router);
        log('🛠️  ESP32 API routes registered');
      } catch (routeError) {
        log('⚠️  ESP32 routes not available - API endpoints disabled');
      }
    } catch (error) {
      log('❌ Failed to initialize ESP32 integration:', error instanceof Error ? error.message : String(error));
      log('🔄 Continuing without ESP32 support...');
    }
  } else {
    log('⚠️  ESP32 integration disabled - Running in software-only mode');
    log('💡 Hardware features (cameras, sensors, locks) will not be available');
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Serve the app on port 5000 (or 8080 as fallback)
  // this serves both the API and the client.
  const port = process.env.PORT || 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    const networkAddresses = getNetworkAddresses();

    log(`🚀 Server running on port ${port}`);
    log(`📱 Local access: http://localhost:${port}`);

    if (networkAddresses.length > 0) {
      log(`🌐 Network access:`);
      networkAddresses.forEach(address => {
        log(`   http://${address}:${port}`);
      });
      log(`📋 Share these URLs with devices on your local network!`);
    } else {
      log(`⚠️  No network interfaces found. Check your network connection.`);
    }

    // ESP32 status information
    if (enableESP32 && esp32Instance) {
      log(`🔧 ESP32 Integration: ${esp32Instance ? 'Active' : 'Failed'}`);
      if (esp32Mode === 'usb') {
        log(`🔌 ESP32 Connection: USB Serial (${process.env.ESP32_BAUD_RATE || '115200'} baud)`);
        log(`🔍 Auto-detection: ${process.env.ESP32_AUTO_DETECT !== 'false' ? 'Enabled' : 'Disabled'}`);
      } else {
        log(`📡 ESP32 WebSocket: ws://${networkAddresses[0] || 'localhost'}:3001`);
      }
      log(`🏠 Smart Locker Features: Enabled`);
      log(`📸 Camera Support: Available`);
      log(`⚖️  Weight Sensors: Available`);
      log(`🔐 Smart Locks: Available`);
    } else {
      log(`🔧 ESP32 Integration: Disabled`);
      log(`💻 Mode: Software-only (no hardware control)`);
    }

    log(`💡 Make sure your firewall allows connections on port ${port}`);
    if (enableESP32) {
      if (esp32Mode === 'usb') {
        log(`🔌 ESP32 devices should be connected via USB`);
      } else {
        log(`🔥 ESP32 devices should connect to port 3001`);
      }
    }
  });
})();
