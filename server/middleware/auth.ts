import { Request, Response, NextFunction } from "express";
import { db } from "../db";
import { users, adminSessions } from "@shared/schema";
import { eq, and } from "drizzle-orm";

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        firstName?: string;
        lastName?: string;
        role: string;
        status: string;
      };
    }
  }
}

// Middleware to require authentication
export const requireAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check for token in Authorization header or session
    let token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token && req.session?.token) {
      token = req.session.token;
    }

    if (!token) {
      return res.status(401).json({ error: "Authentication required" });
    }

    // For admin sessions, check the admin_sessions table
    const adminSession = await db
      .select({
        userId: adminSessions.userId,
        isActive: adminSessions.isActive,
        expiresAt: adminSessions.expiresAt,
      })
      .from(adminSessions)
      .where(and(
        eq(adminSessions.token, token),
        eq(adminSessions.isActive, true)
      ))
      .limit(1);

    let userId: string | undefined;

    if (adminSession.length > 0) {
      const session = adminSession[0];
      
      // Check if session is expired
      if (session.expiresAt < Date.now()) {
        // Mark session as inactive
        await db
          .update(adminSessions)
          .set({ isActive: false })
          .where(eq(adminSessions.token, token));
        
        return res.status(401).json({ error: "Session expired" });
      }

      // Update last activity
      await db
        .update(adminSessions)
        .set({ lastActivity: Date.now() })
        .where(eq(adminSessions.token, token));

      userId = session.userId;
    } else {
      // Fallback to session-based auth for regular users
      userId = req.session?.userId;
    }

    if (!userId) {
      return res.status(401).json({ error: "Invalid authentication token" });
    }

    // Get user details
    const user = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
        status: users.status,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (user.length === 0) {
      return res.status(401).json({ error: "User not found" });
    }

    if (user[0].status !== 'active') {
      return res.status(401).json({ error: "Account is not active" });
    }

    req.user = user[0];
    next();
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({ error: "Authentication failed" });
  }
};

// Middleware to require admin role
export const requireAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ error: "Authentication required" });
  }

  if (!['admin', 'creator'].includes(req.user.role)) {
    return res.status(403).json({ error: "Admin access required" });
  }

  next();
};

// Middleware to require creator role specifically
export const requireCreator = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ error: "Authentication required" });
  }

  if (req.user.role !== 'creator') {
    return res.status(403).json({ error: "Creator access required" });
  }

  next();
};

// Middleware to require agent or admin role
export const requireAgentOrAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ error: "Authentication required" });
  }

  if (!['agent', 'admin', 'creator'].includes(req.user.role)) {
    return res.status(403).json({ error: "Agent or admin access required" });
  }

  next();
};

// Utility function to create admin session
export const createAdminSession = async (userId: string, ipAddress?: string, userAgent?: string) => {
  const sessionId = generateSessionId();
  const token = generateToken();
  const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

  await db.insert(adminSessions).values({
    id: sessionId,
    userId,
    token,
    ipAddress,
    userAgent,
    isActive: true,
    lastActivity: Date.now(),
    expiresAt,
    twoFactorVerified: false,
  });

  return { sessionId, token, expiresAt };
};

// Utility function to invalidate admin session
export const invalidateAdminSession = async (token: string) => {
  await db
    .update(adminSessions)
    .set({ isActive: false })
    .where(eq(adminSessions.token, token));
};

// Utility function to cleanup expired sessions
export const cleanupExpiredSessions = async () => {
  const now = Date.now();
  
  const result = await db
    .update(adminSessions)
    .set({ isActive: false })
    .where(and(
      eq(adminSessions.isActive, true),
      // Sessions expired more than 1 hour ago
      eq(adminSessions.expiresAt, now - (60 * 60 * 1000))
    ));

  return result.changes;
};

// Helper functions
function generateSessionId(): string {
  return 'sess_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function generateToken(): string {
  return 'tok_' + Math.random().toString(36).substring(2) + Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// Schedule cleanup of expired sessions every hour
setInterval(cleanupExpiredSessions, 60 * 60 * 1000);
