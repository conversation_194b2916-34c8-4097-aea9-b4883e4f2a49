import { Router } from "express";
import { db } from "../../db";
import { systemSettings, auditLogs } from "@shared/schema";
import { eq, and } from "drizzle-orm";
import { requireAuth, requireAdmin, requireCreator } from "../../middleware/auth";
import { createAuditLog } from "./audit-logs";

const router = Router();

// Middleware to ensure admin access
router.use(requireAuth);
router.use(requireAdmin);

// Get all system settings
router.get("/", async (req, res) => {
  try {
    const settings = await db.select().from(systemSettings).orderBy(systemSettings.category, systemSettings.key);
    res.json(settings);
  } catch (error) {
    console.error("Error fetching system settings:", error);
    res.status(500).json({ error: "Failed to fetch system settings" });
  }
});

// Get feature flags specifically
router.get("/features", async (req, res) => {
  try {
    const featureFlags = await db.select()
      .from(systemSettings)
      .where(eq(systemSettings.category, "features"))
      .orderBy(systemSettings.key);

    // Convert to a more convenient format
    const features = featureFlags.reduce((acc, flag) => {
      const featureName = flag.key.replace("feature.", "").replace(".enabled", "");
      acc[featureName] = {
        enabled: flag.value === "true",
        description: flag.description,
        isEditable: flag.isEditable
      };
      return acc;
    }, {} as Record<string, any>);

    res.json(features);
  } catch (error) {
    console.error("Error fetching feature flags:", error);
    res.status(500).json({ error: "Failed to fetch feature flags" });
  }
});

// Update feature flags (creator only)
router.put("/features", requireCreator, async (req, res) => {
  try {
    const { features } = req.body;
    const userId = req.user?.id;

    if (!features || typeof features !== 'object') {
      return res.status(400).json({ error: "Invalid features format" });
    }

    const results = [];
    const auditEntries = [];

    for (const [featureName, enabled] of Object.entries(features)) {
      const key = `feature.${featureName}.enabled`;
      const value = enabled ? "true" : "false";

      // Get current setting for audit trail
      const currentSetting = await db.select().from(systemSettings)
        .where(eq(systemSettings.key, key))
        .limit(1);

      if (currentSetting.length === 0) {
        continue; // Skip non-existent settings
      }

      const oldValue = currentSetting[0].value;

      // Update the setting
      const result = await db.update(systemSettings)
        .set({
          value,
          updatedAt: Date.now()
        })
        .where(eq(systemSettings.key, key));

      results.push({ feature: featureName, updated: result.changes > 0 });

      // Create audit log entry
      if (result.changes > 0) {
        auditEntries.push({
          userId: userId!,
          action: "update",
          resource: "feature_flag",
          resourceId: key,
          oldValues: JSON.stringify({ value: oldValue }),
          newValues: JSON.stringify({ value }),
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          severity: "info" as const,
          metadata: JSON.stringify({
            featureName,
            enabled: enabled === true
          }),
        });
      }
    }

    // Insert audit logs
    if (auditEntries.length > 0) {
      await db.insert(auditLogs).values(auditEntries);
    }

    res.json({
      message: "Feature flags updated successfully",
      results,
      updatedCount: results.filter(r => r.updated).length
    });
  } catch (error) {
    console.error("Error updating feature flags:", error);
    res.status(500).json({ error: "Failed to update feature flags" });
  }
});

// Get a specific setting by key
router.get("/:key", async (req, res) => {
  try {
    const { key } = req.params;
    const setting = await db.select().from(systemSettings).where(eq(systemSettings.key, key)).limit(1);
    
    if (setting.length === 0) {
      return res.status(404).json({ error: "Setting not found" });
    }
    
    res.json(setting[0]);
  } catch (error) {
    console.error("Error fetching system setting:", error);
    res.status(500).json({ error: "Failed to fetch system setting" });
  }
});

// Update multiple system settings
router.put("/", async (req, res) => {
  try {
    const { updates } = req.body;
    const userId = req.user?.id;
    
    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({ error: "Invalid updates format" });
    }

    const results = [];
    const auditEntries = [];

    for (const [key, value] of Object.entries(updates)) {
      // Get current setting for audit trail
      const currentSetting = await db.select().from(systemSettings)
        .where(eq(systemSettings.key, key))
        .limit(1);

      if (currentSetting.length === 0) {
        continue; // Skip non-existent settings
      }

      const oldValue = currentSetting[0].value;
      
      // Update the setting
      const result = await db.update(systemSettings)
        .set({ 
          value: value as string, 
          updatedAt: Date.now() 
        })
        .where(and(
          eq(systemSettings.key, key),
          eq(systemSettings.isEditable, true)
        ));

      results.push({ key, updated: result.changes > 0 });

      // Create audit log entry
      if (result.changes > 0) {
        auditEntries.push({
          userId: userId!,
          action: "update",
          resource: "system_setting",
          resourceId: key,
          oldValues: JSON.stringify({ value: oldValue }),
          newValues: JSON.stringify({ value }),
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          severity: "info" as const,
          metadata: JSON.stringify({ 
            settingKey: key,
            category: currentSetting[0].category 
          }),
        });
      }
    }

    // Insert audit logs
    if (auditEntries.length > 0) {
      await db.insert(auditLogs).values(auditEntries);
    }

    res.json({ 
      message: "Settings updated successfully", 
      results,
      updatedCount: results.filter(r => r.updated).length
    });
  } catch (error) {
    console.error("Error updating system settings:", error);
    res.status(500).json({ error: "Failed to update system settings" });
  }
});

// Create a new system setting
router.post("/", async (req, res) => {
  try {
    const { key, value, type, category, description, isEditable = true } = req.body;
    const userId = req.user?.id;

    if (!key || !value || !type || !category) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Check if setting already exists
    const existing = await db.select().from(systemSettings)
      .where(eq(systemSettings.key, key))
      .limit(1);

    if (existing.length > 0) {
      return res.status(409).json({ error: "Setting already exists" });
    }

    const newSetting = await db.insert(systemSettings).values({
      key,
      value,
      type,
      category,
      description,
      isEditable,
    }).returning();

    // Create audit log entry
    await db.insert(auditLogs).values({
      userId: userId!,
      action: "create",
      resource: "system_setting",
      resourceId: key,
      newValues: JSON.stringify({ key, value, type, category, description, isEditable }),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: "info",
      metadata: JSON.stringify({ settingKey: key, category }),
    });

    res.status(201).json(newSetting[0]);
  } catch (error) {
    console.error("Error creating system setting:", error);
    res.status(500).json({ error: "Failed to create system setting" });
  }
});

// Delete a system setting
router.delete("/:key", async (req, res) => {
  try {
    const { key } = req.params;
    const userId = req.user?.id;

    // Get current setting for audit trail
    const currentSetting = await db.select().from(systemSettings)
      .where(eq(systemSettings.key, key))
      .limit(1);

    if (currentSetting.length === 0) {
      return res.status(404).json({ error: "Setting not found" });
    }

    if (!currentSetting[0].isEditable) {
      return res.status(403).json({ error: "Setting is not editable" });
    }

    const result = await db.delete(systemSettings)
      .where(eq(systemSettings.key, key));

    if (result.changes === 0) {
      return res.status(404).json({ error: "Setting not found" });
    }

    // Create audit log entry
    await db.insert(auditLogs).values({
      userId: userId!,
      action: "delete",
      resource: "system_setting",
      resourceId: key,
      oldValues: JSON.stringify(currentSetting[0]),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: "warning",
      metadata: JSON.stringify({ settingKey: key, category: currentSetting[0].category }),
    });

    res.json({ message: "Setting deleted successfully" });
  } catch (error) {
    console.error("Error deleting system setting:", error);
    res.status(500).json({ error: "Failed to delete system setting" });
  }
});

// System backup endpoint
router.post("/backup", async (req, res) => {
  try {
    const userId = req.user?.id;

    // Get all settings
    const settings = await db.select().from(systemSettings);
    
    const backup = {
      timestamp: new Date().toISOString(),
      version: "1.0",
      settings: settings,
      metadata: {
        exportedBy: userId,
        totalSettings: settings.length,
      }
    };

    // Create audit log entry
    await db.insert(auditLogs).values({
      userId: userId!,
      action: "backup",
      resource: "system_setting",
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: "info",
      metadata: JSON.stringify({ 
        settingsCount: settings.length,
        backupSize: JSON.stringify(backup).length 
      }),
    });

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="system-backup-${new Date().toISOString().split('T')[0]}.json"`);
    res.json(backup);
  } catch (error) {
    console.error("Error creating system backup:", error);
    res.status(500).json({ error: "Failed to create system backup" });
  }
});

// System restore endpoint
router.post("/restore", async (req, res) => {
  try {
    const { backup } = req.body;
    const userId = req.user?.id;

    if (!backup || !backup.settings || !Array.isArray(backup.settings)) {
      return res.status(400).json({ error: "Invalid backup format" });
    }

    let restoredCount = 0;
    const errors = [];

    for (const setting of backup.settings) {
      try {
        // Check if setting exists
        const existing = await db.select().from(systemSettings)
          .where(eq(systemSettings.key, setting.key))
          .limit(1);

        if (existing.length > 0) {
          // Update existing setting
          await db.update(systemSettings)
            .set({
              value: setting.value,
              type: setting.type,
              category: setting.category,
              description: setting.description,
              updatedAt: Date.now(),
            })
            .where(eq(systemSettings.key, setting.key));
        } else {
          // Create new setting
          await db.insert(systemSettings).values({
            key: setting.key,
            value: setting.value,
            type: setting.type,
            category: setting.category,
            description: setting.description,
            isEditable: setting.isEditable ?? true,
          });
        }
        restoredCount++;
      } catch (error) {
        errors.push({ key: setting.key, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }

    // Create audit log entry
    await db.insert(auditLogs).values({
      userId: userId!,
      action: "restore",
      resource: "system_setting",
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: errors.length > 0 ? "warning" : "info",
      metadata: JSON.stringify({ 
        restoredCount,
        totalSettings: backup.settings.length,
        errors: errors.length,
        backupVersion: backup.version,
        backupTimestamp: backup.timestamp
      }),
    });

    res.json({
      message: "System restore completed",
      restoredCount,
      totalSettings: backup.settings.length,
      errors
    });
  } catch (error) {
    console.error("Error restoring system backup:", error);
    res.status(500).json({ error: "Failed to restore system backup" });
  }
});

// Initialize default feature flags if they don't exist
export const initializeFeatureFlags = async () => {
  const defaultFeatureFlags = [
    {
      key: "feature.alert_rules.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Alert Rules feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.scheduled_reports.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Scheduled Reports feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.report_builder.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Report Builder feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.bulk_user_operations.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Bulk User Operations feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.predictive_maintenance.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Predictive Maintenance feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.integrations.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Integrations feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.api_monitoring.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable API Monitoring feature in admin panel",
      isEditable: true
    },
    {
      key: "feature.system_settings.enabled",
      value: "true",
      type: "boolean",
      category: "features",
      description: "Enable/disable System Settings feature in admin panel (creator only)",
      isEditable: true
    },
    {
      key: "feature.audit_trail.enabled",
      value: "false",
      type: "boolean",
      category: "features",
      description: "Enable/disable Audit Trail feature in admin panel",
      isEditable: true
    }
  ];

  try {
    for (const flag of defaultFeatureFlags) {
      // Check if the setting already exists
      const existing = await db.select().from(systemSettings)
        .where(eq(systemSettings.key, flag.key))
        .limit(1);

      if (existing.length === 0) {
        // Create the setting if it doesn't exist
        await db.insert(systemSettings).values(flag);
        console.log(`Created feature flag: ${flag.key}`);
      }
    }
  } catch (error) {
    console.error("Error initializing feature flags:", error);
  }
};

export default router;
