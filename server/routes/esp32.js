const express = require('express');
const router = express.Router();
const { db } = require('../database');

// This will be initialized in your main server file
let esp32Integration = null;

// Set the ESP32 integration instance
function setESP32Integration(integration) {
  esp32Integration = integration;
}

// Get all connected ESP32 devices
router.get('/devices', (req, res) => {
  try {
    let devices = [];

    if (esp32Integration) {
      // Get live connected devices
      devices = esp32Integration.getConnectedDevices();
    }

    // Also get devices from database (for USB devices and historical data)
    try {
      const stmt = db.prepare(`
        SELECT device_id, locker_id, connection_type, port_path, status,
               last_ping, battery_level, firmware_version, created_at, updated_at
        FROM esp32_devices
        ORDER BY updated_at DESC
      `);
      const dbDevices = stmt.all();

      // Merge live data with database data
      const mergedDevices = [...devices];

      for (const dbDevice of dbDevices) {
        const existingIndex = mergedDevices.findIndex(d => d.deviceId === dbDevice.device_id);
        if (existingIndex >= 0) {
          // Update existing device with database info
          mergedDevices[existingIndex] = {
            ...mergedDevices[existingIndex],
            ...dbDevice
          };
        } else {
          // Add database-only device
          mergedDevices.push(dbDevice);
        }
      }

      res.json(mergedDevices);
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Fallback to live devices only
      res.json(devices);
    }
  } catch (error) {
    console.error('Error getting devices:', error);
    res.status(500).json({ error: 'Failed to get devices' });
  }
});

// Get locker status
router.get('/locker/:lockerId/status', (req, res) => {
  try {
    const { lockerId } = req.params;
    
    if (!esp32Integration) {
      return res.status(503).json({ error: 'ESP32 integration not initialized' });
    }

    const status = esp32Integration.getLockerStatus(lockerId);
    
    if (!status) {
      return res.status(404).json({ error: 'Locker status not found' });
    }

    res.json({ status });
  } catch (error) {
    console.error('Error getting locker status:', error);
    res.status(500).json({ error: 'Failed to get locker status' });
  }
});

// Unlock locker
router.post('/locker/:lockerId/unlock', (req, res) => {
  try {
    const { lockerId } = req.params;
    const { userId, duration = 300 } = req.body;

    if (!esp32Integration) {
      return res.status(503).json({ error: 'ESP32 integration not initialized' });
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    esp32Integration.unlockLocker(lockerId, userId, duration);
    
    res.json({ 
      success: true, 
      message: `Unlock command sent to locker ${lockerId}`,
      lockerId,
      userId,
      duration
    });
  } catch (error) {
    console.error('Error unlocking locker:', error);
    res.status(500).json({ error: error.message });
  }
});

// Lock locker
router.post('/locker/:lockerId/lock', (req, res) => {
  try {
    const { lockerId } = req.params;

    if (!esp32Integration) {
      return res.status(503).json({ error: 'ESP32 integration not initialized' });
    }

    esp32Integration.lockLocker(lockerId);
    
    res.json({ 
      success: true, 
      message: `Lock command sent to locker ${lockerId}`,
      lockerId
    });
  } catch (error) {
    console.error('Error locking locker:', error);
    res.status(500).json({ error: error.message });
  }
});

// Capture image from locker
router.post('/locker/:lockerId/capture', (req, res) => {
  try {
    const { lockerId } = req.params;

    if (!esp32Integration) {
      return res.status(503).json({ error: 'ESP32 integration not initialized' });
    }

    esp32Integration.captureImage(lockerId);
    
    res.json({ 
      success: true, 
      message: `Image capture command sent to locker ${lockerId}`,
      lockerId
    });
  } catch (error) {
    console.error('Error capturing image:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get locker events history
router.get('/locker/:lockerId/events', (req, res) => {
  try {
    const { lockerId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    const stmt = db.prepare(`
      SELECT * FROM locker_events 
      WHERE locker_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ? OFFSET ?
    `);
    
    const events = stmt.all(lockerId, parseInt(limit), parseInt(offset));
    
    res.json({ events });
  } catch (error) {
    console.error('Error getting locker events:', error);
    res.status(500).json({ error: 'Failed to get locker events' });
  }
});

// Get locker images
router.get('/locker/:lockerId/images', (req, res) => {
  try {
    const { lockerId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    const stmt = db.prepare(`
      SELECT id, locker_id, image_format, event_type, timestamp 
      FROM locker_images 
      WHERE locker_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ? OFFSET ?
    `);
    
    const images = stmt.all(lockerId, parseInt(limit), parseInt(offset));
    
    res.json({ images });
  } catch (error) {
    console.error('Error getting locker images:', error);
    res.status(500).json({ error: 'Failed to get locker images' });
  }
});

// Get specific image
router.get('/locker/:lockerId/images/:imageId', (req, res) => {
  try {
    const { lockerId, imageId } = req.params;

    const stmt = db.prepare(`
      SELECT image_data, image_format 
      FROM locker_images 
      WHERE id = ? AND locker_id = ?
    `);
    
    const image = stmt.get(imageId, lockerId);
    
    if (!image) {
      return res.status(404).json({ error: 'Image not found' });
    }

    res.set({
      'Content-Type': `image/${image.image_format}`,
      'Content-Length': image.image_data.length
    });
    
    res.send(image.image_data);
  } catch (error) {
    console.error('Error getting image:', error);
    res.status(500).json({ error: 'Failed to get image' });
  }
});

// Get locker status history
router.get('/locker/:lockerId/status-history', (req, res) => {
  try {
    const { lockerId } = req.params;
    const { limit = 100, offset = 0, from, to } = req.query;

    let query = `
      SELECT * FROM locker_status 
      WHERE locker_id = ?
    `;
    
    const params = [lockerId];

    if (from) {
      query += ` AND timestamp >= ?`;
      params.push(from);
    }

    if (to) {
      query += ` AND timestamp <= ?`;
      params.push(to);
    }

    query += ` ORDER BY timestamp DESC LIMIT ? OFFSET ?`;
    params.push(parseInt(limit), parseInt(offset));

    const stmt = db.prepare(query);
    const statusHistory = stmt.all(...params);
    
    res.json({ statusHistory });
  } catch (error) {
    console.error('Error getting status history:', error);
    res.status(500).json({ error: 'Failed to get status history' });
  }
});

// Get system statistics
router.get('/stats', (req, res) => {
  try {
    // Get device statistics
    const deviceStats = db.prepare(`
      SELECT 
        COUNT(*) as total_devices,
        SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online_devices,
        SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline_devices
      FROM esp32_devices
    `).get();

    // Get event statistics for last 24 hours
    const eventStats = db.prepare(`
      SELECT 
        event_type,
        COUNT(*) as count
      FROM locker_events 
      WHERE timestamp >= datetime('now', '-24 hours')
      GROUP BY event_type
    `).all();

    // Get locker occupancy statistics
    const occupancyStats = db.prepare(`
      SELECT 
        locker_id,
        is_occupied,
        current_weight,
        timestamp
      FROM locker_status ls1
      WHERE timestamp = (
        SELECT MAX(timestamp) 
        FROM locker_status ls2 
        WHERE ls2.locker_id = ls1.locker_id
      )
    `).all();

    res.json({
      devices: deviceStats,
      events: eventStats,
      occupancy: occupancyStats
    });
  } catch (error) {
    console.error('Error getting statistics:', error);
    res.status(500).json({ error: 'Failed to get statistics' });
  }
});

// Emergency unlock all lockers
router.post('/emergency/unlock-all', (req, res) => {
  try {
    if (!esp32Integration) {
      return res.status(503).json({ error: 'ESP32 integration not initialized' });
    }

    const { reason = 'Emergency unlock' } = req.body;
    const devices = esp32Integration.getConnectedDevices();
    const unlockedLockers = [];

    devices.forEach(device => {
      try {
        esp32Integration.unlockLocker(device.lockerId, 'EMERGENCY', 3600); // 1 hour
        unlockedLockers.push(device.lockerId);
        
        // Log emergency event
        const stmt = db.prepare(`
          INSERT INTO locker_events (locker_id, event_type, details)
          VALUES (?, 'emergency_unlock', ?)
        `);
        stmt.run(device.lockerId, JSON.stringify({ reason }));
      } catch (error) {
        console.error(`Failed to unlock locker ${device.lockerId}:`, error);
      }
    });

    res.json({
      success: true,
      message: 'Emergency unlock initiated',
      unlockedLockers,
      reason
    });
  } catch (error) {
    console.error('Error during emergency unlock:', error);
    res.status(500).json({ error: 'Failed to execute emergency unlock' });
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  try {
    const isESP32Ready = esp32Integration !== null;
    const connectedDevices = isESP32Ready ? esp32Integration.getConnectedDevices().length : 0;

    res.json({
      status: 'ok',
      esp32Integration: isESP32Ready,
      connectedDevices,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in health check:', error);
    res.status(500).json({ error: 'Health check failed' });
  }
});

module.exports = { router, setESP32Integration };
