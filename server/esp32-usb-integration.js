import { SerialPort } from 'serialport';
import { ReadlineParser } from '@serialport/parser-readline';
import { db, sqlite } from './db.js';

class ESP32USBIntegration {
  constructor(options = {}) {
    this.baudRate = options.baudRate || 115200;
    this.autoDetect = options.autoDetect !== false;
    this.connectedDevices = new Map();
    this.serialPorts = new Map();
    this.imageChunks = new Map();
    this.reconnectInterval = options.reconnectInterval || 5000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    
    this.setupDatabaseTables();
    
    if (this.autoDetect) {
      this.startAutoDetection();
    }
  }

  async setupDatabaseTables() {
    try {
      // Create ESP32 devices table if it doesn't exist
      sqlite.exec(`
        CREATE TABLE IF NOT EXISTS esp32_devices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          device_id TEXT UNIQUE NOT NULL,
          locker_id INTEGER,
          connection_type TEXT DEFAULT 'usb',
          port_path TEXT,
          status TEXT DEFAULT 'offline',
          last_ping INTEGER,
          battery_level INTEGER,
          firmware_version TEXT,
          created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
          updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
        )
      `);

      // Create locker events table if it doesn't exist
      sqlite.exec(`
        CREATE TABLE IF NOT EXISTS locker_events (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          device_id TEXT NOT NULL,
          locker_id INTEGER,
          event_type TEXT NOT NULL,
          event_data TEXT,
          timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000)
        )
      `);

      console.log('✅ ESP32 USB database tables initialized');
    } catch (error) {
      console.error('❌ Error setting up ESP32 USB database tables:', error);
    }
  }

  async startAutoDetection() {
    console.log('🔍 Starting ESP32 USB auto-detection...');
    
    try {
      const ports = await SerialPort.list();
      const esp32Ports = ports.filter(port => 
        port.manufacturer && (
          port.manufacturer.toLowerCase().includes('espressif') ||
          port.manufacturer.toLowerCase().includes('silicon labs') ||
          port.manufacturer.toLowerCase().includes('cp210') ||
          port.vendorId === '10c4' || // Silicon Labs
          port.vendorId === '1a86'    // CH340
        )
      );

      console.log(`📡 Found ${esp32Ports.length} potential ESP32 device(s)`);
      
      for (const portInfo of esp32Ports) {
        await this.connectToPort(portInfo.path, portInfo);
      }

      // Set up periodic scanning for new devices
      setInterval(() => {
        this.scanForNewDevices();
      }, 10000); // Scan every 10 seconds

    } catch (error) {
      console.error('❌ Error during ESP32 auto-detection:', error);
    }
  }

  async scanForNewDevices() {
    try {
      const ports = await SerialPort.list();
      const esp32Ports = ports.filter(port => 
        port.manufacturer && (
          port.manufacturer.toLowerCase().includes('espressif') ||
          port.manufacturer.toLowerCase().includes('silicon labs') ||
          port.manufacturer.toLowerCase().includes('cp210') ||
          port.vendorId === '10c4' ||
          port.vendorId === '1a86'
        )
      );

      for (const portInfo of esp32Ports) {
        if (!this.serialPorts.has(portInfo.path)) {
          console.log(`🔌 New ESP32 device detected on ${portInfo.path}`);
          await this.connectToPort(portInfo.path, portInfo);
        }
      }
    } catch (error) {
      console.error('❌ Error scanning for new devices:', error);
    }
  }

  async connectToPort(portPath, portInfo = {}) {
    try {
      console.log(`🔌 Connecting to ESP32 on ${portPath}...`);
      
      const port = new SerialPort({
        path: portPath,
        baudRate: this.baudRate,
        autoOpen: false
      });

      const parser = port.pipe(new ReadlineParser({ delimiter: '\n' }));
      
      port.open((err) => {
        if (err) {
          console.error(`❌ Failed to open port ${portPath}:`, err.message);
          return;
        }
        
        console.log(`✅ Connected to ESP32 on ${portPath}`);
        this.serialPorts.set(portPath, { port, parser, portInfo, reconnectAttempts: 0 });
        
        // Send identification request
        setTimeout(() => {
          this.sendCommand(portPath, { type: 'identify' });
        }, 2000);
      });

      parser.on('data', (data) => {
        this.handleSerialData(portPath, data.trim());
      });

      port.on('error', (err) => {
        console.error(`❌ Serial port error on ${portPath}:`, err.message);
        this.handlePortDisconnection(portPath);
      });

      port.on('close', () => {
        console.log(`🔌 Serial port ${portPath} closed`);
        this.handlePortDisconnection(portPath);
      });

    } catch (error) {
      console.error(`❌ Error connecting to port ${portPath}:`, error);
    }
  }

  handleSerialData(portPath, data) {
    try {
      // Try to parse as JSON
      const message = JSON.parse(data);
      this.handleMessage(portPath, message);
    } catch (error) {
      // Handle non-JSON data (debug messages, etc.)
      console.log(`📝 ESP32 ${portPath}: ${data}`);
    }
  }

  handleMessage(portPath, message) {
    try {
      const { type, deviceId, lockerId } = message;
      
      switch (type) {
        case 'identify_response':
          this.registerDevice(portPath, message);
          break;
          
        case 'status_update':
          this.updateDeviceStatus(deviceId, message);
          break;
          
        case 'sensor_data':
          this.handleSensorData(deviceId, message);
          break;
          
        case 'image_chunk':
          this.handleImageChunk(deviceId, message);
          break;
          
        case 'command_response':
          this.handleCommandResponse(deviceId, message);
          break;
          
        case 'error':
          console.error(`❌ ESP32 ${deviceId} error:`, message.error);
          break;
          
        default:
          console.log(`📨 Unknown message type from ${deviceId}:`, message);
      }
      
      // Log event to database
      this.logEvent(message.deviceId || 'unknown', message.lockerId, type, message);
      
    } catch (error) {
      console.error('❌ Error handling ESP32 message:', error);
    }
  }

  registerDevice(portPath, message) {
    const { deviceId, lockerId, firmwareVersion, deviceType } = message;
    
    console.log(`📋 Registering ESP32 device: ${deviceId} (Locker ${lockerId})`);
    
    // Store device info
    this.connectedDevices.set(deviceId, {
      deviceId,
      lockerId,
      portPath,
      firmwareVersion,
      deviceType,
      status: 'online',
      lastPing: Date.now(),
      connectedAt: Date.now()
    });

    // Update database
    try {
      const stmt = sqlite.prepare(`
        INSERT OR REPLACE INTO esp32_devices
        (device_id, locker_id, connection_type, port_path, status, last_ping, firmware_version, updated_at)
        VALUES (?, ?, 'usb', ?, 'online', ?, ?, ?)
      `);

      stmt.run(deviceId, lockerId, portPath, Date.now(), firmwareVersion, Date.now());
      console.log(`✅ Device ${deviceId} registered in database`);
    } catch (error) {
      console.error('❌ Error registering device in database:', error);
    }

    // Send welcome message
    this.sendCommand(portPath, {
      type: 'welcome',
      message: 'Connected to SwiSto server',
      timestamp: Date.now()
    });
  }

  updateDeviceStatus(deviceId, message) {
    const device = this.connectedDevices.get(deviceId);
    if (!device) return;

    // Update device info
    Object.assign(device, {
      status: message.status || device.status,
      batteryLevel: message.batteryLevel || device.batteryLevel,
      lastPing: Date.now()
    });

    // Update database
    try {
      const stmt = sqlite.prepare(`
        UPDATE esp32_devices
        SET status = ?, battery_level = ?, last_ping = ?, updated_at = ?
        WHERE device_id = ?
      `);

      stmt.run(device.status, device.batteryLevel, device.lastPing, Date.now(), deviceId);
    } catch (error) {
      console.error('❌ Error updating device status:', error);
    }
  }

  handleSensorData(deviceId, message) {
    const { weight, temperature, humidity, motion } = message;
    
    // Update locker status based on sensor data
    if (weight !== undefined) {
      const isOccupied = weight > 50; // Threshold for occupied status
      this.updateLockerOccupancy(message.lockerId, isOccupied, weight);
    }
    
    console.log(`📊 Sensor data from ${deviceId}:`, { weight, temperature, humidity, motion });
  }

  updateLockerOccupancy(lockerId, isOccupied, weight) {
    try {
      // Update locker status in main database
      const stmt = sqlite.prepare(`
        UPDATE lockers
        SET status = ?, current_weight = ?, last_ping = ?, updated_at = ?
        WHERE id = ?
      `);

      const status = isOccupied ? 'occupied' : 'available';
      stmt.run(status, weight, Date.now(), Date.now(), lockerId);

      console.log(`📦 Locker ${lockerId} status updated: ${status} (weight: ${weight}g)`);
    } catch (error) {
      console.error('❌ Error updating locker occupancy:', error);
    }
  }

  sendCommand(portPath, command) {
    const portData = this.serialPorts.get(portPath);
    if (!portData || !portData.port.isOpen) {
      console.error(`❌ Cannot send command to ${portPath}: port not open`);
      return false;
    }

    try {
      const commandStr = JSON.stringify(command) + '\n';
      portData.port.write(commandStr);
      console.log(`📤 Sent command to ${portPath}:`, command);
      return true;
    } catch (error) {
      console.error(`❌ Error sending command to ${portPath}:`, error);
      return false;
    }
  }

  // Public API methods
  openLocker(lockerId) {
    const device = this.findDeviceByLockerId(lockerId);
    if (!device) {
      throw new Error(`No ESP32 device found for locker ${lockerId}`);
    }
    
    return this.sendCommand(device.portPath, {
      type: 'command',
      command: 'open',
      lockerId
    });
  }

  closeLocker(lockerId) {
    const device = this.findDeviceByLockerId(lockerId);
    if (!device) {
      throw new Error(`No ESP32 device found for locker ${lockerId}`);
    }
    
    return this.sendCommand(device.portPath, {
      type: 'command',
      command: 'close',
      lockerId
    });
  }

  captureImage(lockerId) {
    const device = this.findDeviceByLockerId(lockerId);
    if (!device) {
      throw new Error(`No ESP32 device found for locker ${lockerId}`);
    }
    
    return this.sendCommand(device.portPath, {
      type: 'command',
      command: 'capture_image',
      lockerId
    });
  }

  findDeviceByLockerId(lockerId) {
    for (const device of this.connectedDevices.values()) {
      if (device.lockerId == lockerId) {
        return device;
      }
    }
    return null;
  }

  getConnectedDevices() {
    return Array.from(this.connectedDevices.values());
  }

  handlePortDisconnection(portPath) {
    // Find and remove disconnected device
    for (const [deviceId, device] of this.connectedDevices.entries()) {
      if (device.portPath === portPath) {
        console.log(`🔌 Device ${deviceId} disconnected`);
        this.connectedDevices.delete(deviceId);
        
        // Update database
        try {
          const stmt = sqlite.prepare(`
            UPDATE esp32_devices
            SET status = 'offline', updated_at = ?
            WHERE device_id = ?
          `);
          stmt.run(Date.now(), deviceId);
        } catch (error) {
          console.error('❌ Error updating disconnected device:', error);
        }
        break;
      }
    }

    // Clean up port
    this.serialPorts.delete(portPath);
    
    // Attempt reconnection
    setTimeout(() => {
      this.attemptReconnection(portPath);
    }, this.reconnectInterval);
  }

  async attemptReconnection(portPath) {
    const portData = this.serialPorts.get(portPath);
    if (portData && portData.reconnectAttempts < this.maxReconnectAttempts) {
      console.log(`🔄 Attempting to reconnect to ${portPath} (attempt ${portData.reconnectAttempts + 1})`);
      portData.reconnectAttempts++;
      await this.connectToPort(portPath, portData.portInfo);
    }
  }

  logEvent(deviceId, lockerId, eventType, eventData) {
    try {
      const stmt = sqlite.prepare(`
        INSERT INTO locker_events (device_id, locker_id, event_type, event_data)
        VALUES (?, ?, ?, ?)
      `);

      stmt.run(deviceId, lockerId, eventType, JSON.stringify(eventData));
    } catch (error) {
      console.error('❌ Error logging event:', error);
    }
  }

  handleImageChunk(deviceId, message) {
    const { chunkIndex, totalChunks, data, imageId } = message;
    
    if (!this.imageChunks.has(imageId)) {
      this.imageChunks.set(imageId, {
        chunks: new Array(totalChunks),
        receivedChunks: 0,
        deviceId,
        startTime: Date.now()
      });
    }
    
    const imageData = this.imageChunks.get(imageId);
    imageData.chunks[chunkIndex] = data;
    imageData.receivedChunks++;
    
    if (imageData.receivedChunks === totalChunks) {
      // All chunks received, reconstruct image
      const completeImage = imageData.chunks.join('');
      this.handleCompleteImage(deviceId, imageId, completeImage);
      this.imageChunks.delete(imageId);
    }
  }

  handleCompleteImage(deviceId, imageId, imageData) {
    console.log(`📸 Complete image received from ${deviceId} (ID: ${imageId})`);
    
    // Here you could save the image to disk, database, or process it
    // For now, just log the event
    this.logEvent(deviceId, null, 'image_captured', {
      imageId,
      imageSize: imageData.length,
      timestamp: Date.now()
    });
  }

  handleCommandResponse(deviceId, message) {
    const { command, success, error, lockerId } = message;
    
    if (success) {
      console.log(`✅ Command '${command}' executed successfully on locker ${lockerId}`);
    } else {
      console.error(`❌ Command '${command}' failed on locker ${lockerId}:`, error);
    }
    
    this.logEvent(deviceId, lockerId, 'command_response', message);
  }

  // Cleanup method
  async disconnect() {
    console.log('🔌 Disconnecting all ESP32 USB devices...');
    
    for (const [portPath, portData] of this.serialPorts.entries()) {
      try {
        if (portData.port.isOpen) {
          await new Promise((resolve) => {
            portData.port.close(resolve);
          });
        }
      } catch (error) {
        console.error(`❌ Error closing port ${portPath}:`, error);
      }
    }
    
    this.serialPorts.clear();
    this.connectedDevices.clear();
    console.log('✅ All ESP32 USB connections closed');
  }
}

export default ESP32USBIntegration;
