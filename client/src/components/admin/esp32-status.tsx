import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useEffect, useState } from "react";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { Wifi, WifiOff, Activity, RefreshCw, Zap, AlertTriangle, Usb, Cable } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function ESP32Status() {
  const { lastMessage } = useWebSocket();
  const { toast } = useToast();
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const { data: lockers = [], isLoading } = useQuery({
    queryKey: ["/api/lockers"],
    refetchInterval: 3000,
  });

  const { data: esp32Commands = [] } = useQuery({
    queryKey: ["/api/esp32/commands"],
    refetchInterval: 2000,
  });

  const { data: esp32Devices = [] } = useQuery({
    queryKey: ["/api/esp32/devices"],
    refetchInterval: 3000,
  });

  // Real-time updates
  useEffect(() => {
    if (lastMessage) {
      if (lastMessage.type === 'esp32_command' || lastMessage.type === 'esp32_status') {
        queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
        queryClient.invalidateQueries({ queryKey: ["/api/esp32/commands"] });
      }
    }
  }, [lastMessage]);

  const getConnectionStatus = (locker: any) => {
    const lastPing = locker.lastPing ? new Date(locker.lastPing) : null;
    const now = new Date();
    const minutesAgo = lastPing ? (now.getTime() - lastPing.getTime()) / (1000 * 60) : null;

    // Find corresponding ESP32 device
    const esp32Device = esp32Devices.find((device: any) => device.locker_id == locker.id);
    const connectionType = esp32Device?.connection_type || 'wifi';
    const isUSB = connectionType === 'usb';

    if (!lastPing || (minutesAgo !== null && minutesAgo > 5)) {
      return {
        status: 'offline',
        color: 'bg-red-100 text-red-800',
        icon: isUSB ? Cable : WifiOff,
        connectionType
      };
    } else if (minutesAgo !== null && minutesAgo > 2) {
      return {
        status: 'unstable',
        color: 'bg-yellow-100 text-yellow-800',
        icon: AlertTriangle,
        connectionType
      };
    } else {
      return {
        status: 'online',
        color: 'bg-green-100 text-green-800',
        icon: isUSB ? Usb : Wifi,
        connectionType
      };
    }
  };

  const testESP32Connection = async (lockerId: number) => {
    setIsTestingConnection(true);
    try {
      await apiRequest("POST", "/api/esp32/ping", { lockerId });
      toast({
        title: "Ping Sent",
        description: `Ping command sent to ESP32 for locker ${lockerId}`,
      });
    } catch (error) {
      toast({
        title: "Connection Test Failed",
        description: "Failed to send ping command to ESP32",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const resetESP32 = async (lockerId: number) => {
    try {
      await apiRequest("POST", "/api/esp32/reset", { lockerId });
      toast({
        title: "Reset Command Sent",
        description: `Reset command sent to ESP32 for locker ${lockerId}`,
      });
    } catch (error) {
      toast({
        title: "Reset Failed",
        description: "Failed to send reset command to ESP32",
        variant: "destructive",
      });
    }
  };

  const connectedDevices = lockers.filter((locker: any) => 
    getConnectionStatus(locker).status === 'online'
  );

  const offlineDevices = lockers.filter((locker: any) => 
    getConnectionStatus(locker).status === 'offline'
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            ESP32 System Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              ESP32 System Status
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => queryClient.invalidateQueries({ queryKey: ["/api/lockers"] })}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-800">Online Devices</p>
                  <p className="text-2xl font-bold text-green-900">{connectedDevices.length}</p>
                </div>
                <Wifi className="w-8 h-8 text-green-600" />
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-800">Offline Devices</p>
                  <p className="text-2xl font-bold text-red-900">{offlineDevices.length}</p>
                </div>
                <WifiOff className="w-8 h-8 text-red-600" />
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-800">Total Devices</p>
                  <p className="text-2xl font-bold text-blue-900">{lockers.length}</p>
                </div>
                <Zap className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Device Details</h3>
            {lockers.map((locker: any) => {
              const connectionInfo = getConnectionStatus(locker);
              const Icon = connectionInfo.icon;
              
              return (
                <div key={locker.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium">Locker {locker.code}</div>
                        <div className="text-sm text-gray-500">ESP32 ID: {locker.esp32Id}</div>
                        <div className="text-sm text-gray-500">
                          Connection: {connectionInfo.connectionType === 'usb' ? 'USB Serial' : 'WiFi'}
                        </div>
                        {locker.lastPing && (
                          <div className="text-xs text-gray-400">
                            Last ping: {new Date(locker.lastPing).toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Badge className={connectionInfo.color}>
                        {connectionInfo.status}
                      </Badge>
                      
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testESP32Connection(locker.id)}
                          disabled={isTestingConnection}
                        >
                          Ping
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => resetESP32(locker.id)}
                        >
                          Reset
                        </Button>
                      </div>
                    </div>
                  </div>

                  {locker.batteryLevel && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">Battery:</span>
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              locker.batteryLevel > 50 ? 'bg-green-500' : 
                              locker.batteryLevel > 20 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${locker.batteryLevel}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{locker.batteryLevel}%</span>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* USB Devices Information */}
      {esp32Devices.some((device: any) => device.connection_type === 'usb') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Usb className="w-5 h-5" />
              <span>USB Connected Devices</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {esp32Devices
                .filter((device: any) => device.connection_type === 'usb')
                .map((device: any) => (
                  <div key={device.device_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Cable className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium">Device: {device.device_id}</div>
                          <div className="text-sm text-gray-500">Port: {device.port_path}</div>
                          <div className="text-sm text-gray-500">Locker: {device.locker_id}</div>
                          {device.firmware_version && (
                            <div className="text-xs text-gray-400">
                              Firmware: {device.firmware_version}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={
                          device.status === 'online' ? 'bg-green-100 text-green-800' :
                          device.status === 'offline' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }>
                          {device.status}
                        </Badge>
                        {device.last_ping && (
                          <div className="text-xs text-gray-400">
                            {new Date(device.last_ping).toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent ESP32 Commands */}
      <Card>
        <CardHeader>
          <CardTitle>Recent ESP32 Commands</CardTitle>
        </CardHeader>
        <CardContent>
          {esp32Commands.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No recent commands</p>
          ) : (
            <div className="space-y-3">
              {esp32Commands.slice(0, 10).map((command: any) => (
                <div key={command.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">Locker {command.lockerId}</div>
                    <div className="text-sm text-gray-600">Command: {command.command}</div>
                    <div className="text-xs text-gray-400">
                      {new Date(command.createdAt).toLocaleString()}
                    </div>
                  </div>
                  <Badge 
                    className={
                      command.status === 'completed' ? 'bg-green-100 text-green-800' :
                      command.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }
                  >
                    {command.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}