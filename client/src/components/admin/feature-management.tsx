import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFeatureFlags, FeatureFlags } from "@/hooks/useFeatureFlags";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Save, RotateCcw, Shield, AlertTriangle } from "lucide-react";

const featureDescriptions: Record<keyof FeatureFlags, { title: string; description: string; category: string }> = {
  alert_rules: {
    title: "Alert Rules",
    description: "Configure custom alert rules and notifications for system monitoring",
    category: "Monitoring"
  },
  scheduled_reports: {
    title: "Scheduled Reports",
    description: "Set up automated report generation and delivery schedules",
    category: "Reporting"
  },
  report_builder: {
    title: "Report Builder",
    description: "Create custom reports with advanced filtering and visualization options",
    category: "Reporting"
  },
  bulk_user_operations: {
    title: "Bulk User Operations",
    description: "Perform batch operations on multiple users simultaneously",
    category: "User Management"
  },
  predictive_maintenance: {
    title: "Predictive Maintenance",
    description: "AI-powered predictive maintenance and failure prevention system",
    category: "Maintenance"
  },
  integrations: {
    title: "Integrations",
    description: "Manage third-party integrations and API connections",
    category: "Integration"
  },
  api_monitoring: {
    title: "API Monitoring",
    description: "Monitor API performance, usage, and health metrics",
    category: "Monitoring"
  },
  system_settings: {
    title: "System Settings",
    description: "Configure global system settings and parameters",
    category: "System"
  },
  audit_trail: {
    title: "Audit Trail",
    description: "View detailed audit logs and system activity tracking",
    category: "Security"
  }
};

export function FeatureManagement() {
  const { features, isLoading } = useFeatureFlags();
  const [localFeatures, setLocalFeatures] = useState<Record<string, boolean>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Initialize local state when features load
  useState(() => {
    if (features && Object.keys(localFeatures).length === 0) {
      const initialState = Object.entries(features).reduce((acc, [key, flag]) => {
        acc[key] = flag.enabled;
        return acc;
      }, {} as Record<string, boolean>);
      setLocalFeatures(initialState);
    }
  });

  const updateFeaturesMutation = useMutation({
    mutationFn: async (featureUpdates: Record<string, boolean>) => {
      const response = await fetch("/api/admin/system-settings/features", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ features: featureUpdates }),
      });
      if (!response.ok) throw new Error("Failed to update features");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/system-settings/features"] });
      setHasChanges(false);
      toast({
        title: "Features Updated",
        description: "Feature settings have been successfully updated.",
      });
    },
    onError: () => {
      toast({
        title: "Update Failed",
        description: "Failed to update feature settings. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleFeatureToggle = (featureName: string, enabled: boolean) => {
    setLocalFeatures(prev => ({ ...prev, [featureName]: enabled }));
    setHasChanges(true);
  };

  const handleSave = () => {
    updateFeaturesMutation.mutate(localFeatures);
  };

  const handleReset = () => {
    if (features) {
      const resetState = Object.entries(features).reduce((acc, [key, flag]) => {
        acc[key] = flag.enabled;
        return acc;
      }, {} as Record<string, boolean>);
      setLocalFeatures(resetState);
      setHasChanges(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  const categories = Array.from(new Set(Object.values(featureDescriptions).map(f => f.category)));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Feature Management</h1>
          <p className="text-gray-600">Control the visibility of admin features</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="flex items-center space-x-1">
            <Shield className="w-3 h-3" />
            <span>Creator Only</span>
          </Badge>
        </div>
      </div>

      {hasChanges && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-orange-600" />
                <span className="text-sm text-orange-800">You have unsaved changes</span>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleReset}>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Reset
                </Button>
                <Button size="sm" onClick={handleSave} disabled={updateFeaturesMutation.isPending}>
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {categories.map(category => (
        <Card key={category}>
          <CardHeader>
            <CardTitle className="text-lg">{category}</CardTitle>
            <CardDescription>
              Manage {category.toLowerCase()} features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(featureDescriptions)
              .filter(([_, desc]) => desc.category === category)
              .map(([featureName, desc]) => {
                const isEnabled = localFeatures[featureName] ?? false;
                const isSystemSettings = featureName === 'system_settings';
                
                return (
                  <div key={featureName} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">{desc.title}</h3>
                        {isSystemSettings && (
                          <Badge variant="secondary" className="text-xs">
                            Always Visible to Creator
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{desc.description}</p>
                    </div>
                    <Switch
                      checked={isEnabled}
                      onCheckedChange={(checked) => handleFeatureToggle(featureName, checked)}
                      disabled={isSystemSettings} // System settings always visible to creator
                    />
                  </div>
                );
              })}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
