import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Package, Users, Truck, AlertTriangle } from "lucide-react";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useEffect } from "react";
import { queryClient } from "@/lib/queryClient";

export default function StatsCards() {
  const { lastMessage } = useWebSocket();

  const { data: stats, isLoading } = useQuery({
    queryKey: ["/api/stats"],
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const { data: users } = useQuery({
    queryKey: ["/api/users"],
    refetchInterval: 15000,
  });

  const { data: bookings } = useQuery({
    queryKey: ["/api/bookings"],
    refetchInterval: 5000,
  });

  // Real-time updates via WebSocket
  useEffect(() => {
    if (lastMessage) {
      const messageTypes = [
        'locker_status_updated',
        'booking_created',
        'booking_updated',
        'user_registered',
        'payment_processed'
      ];
      
      if (messageTypes.includes(lastMessage.type)) {
        queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
        queryClient.invalidateQueries({ queryKey: ["/api/users"] });
        queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      }
    }
  }, [lastMessage]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const agentUsers = Array.isArray(users) ? users.filter((user: any) => user.role === 'agent' && !user.id.startsWith('guest-')) : [];
  const regularUsers = Array.isArray(users) ? users.filter((user: any) => user.role === 'user' && !user.id.startsWith('guest-')) : [];
  const activeBookings = Array.isArray(bookings) ? bookings.filter((booking: any) => booking.status === 'active' || booking.status === 'pending') : [];
  const completedBookings = Array.isArray(bookings) ? bookings.filter((booking: any) => booking.status === 'completed') : [];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Lockers</p>
              <p className="text-3xl font-bold text-gray-900">
                {(stats as any)?.totalLockers || 0}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Occupied: {(stats as any)?.occupiedLockers || 0} | Available: {(stats as any)?.availableLockers || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <Package className="w-6 h-6 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Users</p>
              <p className="text-3xl font-bold text-gray-900">
                {regularUsers.length}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Registered users in system
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Swift Agents</p>
              <p className="text-3xl font-bold text-gray-900">
                {agentUsers.length}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Available delivery agents
              </p>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <Truck className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Bookings</p>
              <p className="text-3xl font-bold text-gray-900">
                {activeBookings.length}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Completed: {completedBookings.length}
              </p>
            </div>
            <div className="p-3 bg-red-50 rounded-full">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
