import { cn } from "@/lib/utils";
import { BarChart3, Users, Package, AlertTriangle, Activity, Wifi, UserCheck, MessageCircle, Monitor, Settings, Shield, Bell, FileText, PieChart, UserCog, Wrench, Link, Globe } from "lucide-react";
import { useFeatureFlags } from "@/hooks/useFeatureFlags";
import { useAdminAuth } from "@/hooks/useAdminAuth";

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface NavigationItem {
  name: string;
  id: string;
  icon: any;
  featureFlag?: string; // Optional feature flag to control visibility
  requiresCreator?: boolean; // Requires creator role
}

const navigation: NavigationItem[] = [
  { name: "Dashboard", id: "dashboard", icon: BarChart3 },
  { name: "Lockers", id: "lockers", icon: Package },
  { name: "Locker Status", id: "locker-status", icon: Monitor },
  { name: "User Management", id: "user-management", icon: Users },
  { name: "Support Chat", id: "support", icon: MessageCircle },
  { name: "ESP32 Status", id: "esp32", icon: Wifi },
  { name: "Alerts & Logs", id: "alerts", icon: AlertTriangle },
  { name: "Analytics", id: "analytics", icon: Activity },
  { name: "Alert Rules", id: "alert-rules", icon: Bell, featureFlag: "alert_rules" },
  { name: "Scheduled Reports", id: "scheduled-reports", icon: FileText, featureFlag: "scheduled_reports" },
  { name: "Report Builder", id: "report-builder", icon: PieChart, featureFlag: "report_builder" },
  { name: "Bulk User Ops", id: "bulk-user-operations", icon: UserCog, featureFlag: "bulk_user_operations" },
  { name: "Predictive Maintenance", id: "predictive-maintenance", icon: Wrench, featureFlag: "predictive_maintenance" },
  { name: "Integrations", id: "integration-management", icon: Link, featureFlag: "integrations" },
  { name: "API Monitoring", id: "api-monitoring", icon: Globe, featureFlag: "api_monitoring" },
  { name: "System Settings", id: "system-settings", icon: Settings, featureFlag: "system_settings", requiresCreator: true },
  { name: "Audit Trail", id: "audit-trail", icon: Shield, featureFlag: "audit_trail" },
];

export function Sidebar({ activeSection, onSectionChange }: SidebarProps) {
  const { isFeatureEnabled } = useFeatureFlags();
  const { adminUser } = useAdminAuth();

  // Filter navigation items based on feature flags and user role
  const visibleNavigation = navigation.filter((item) => {
    // If item has a feature flag, check if it's enabled
    if (item.featureFlag) {
      const isEnabled = isFeatureEnabled(item.featureFlag as any);
      if (!isEnabled) return false;
    }

    // If item requires creator role, check user role
    if (item.requiresCreator) {
      return adminUser?.role === 'creator';
    }

    return true;
  });

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-sm border-r border-gray-200 lg:block hidden">
      <div className="flex flex-col h-full">
        <div className="p-6 border-b">
          <h1 className="text-xl font-bold text-gray-900">SwiSto Admin</h1>
          {adminUser?.role === 'creator' && (
            <p className="text-xs text-blue-600 mt-1">Creator Access</p>
          )}
        </div>
        <nav className="flex-1 px-6 py-6 space-y-2">
          {visibleNavigation.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                className={cn(
                  "w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                  activeSection === item.id
                    ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                    : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                )}
              >
                <Icon className="mr-3 h-5 w-5" />
                {item.name}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
