import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Sidebar } from "@/components/admin/sidebar";
import { Dashboard } from "@/components/admin/dashboard";
import { LockersGrid } from "@/components/admin/lockers-grid";
import { ActivityFeed } from "@/components/admin/activity-feed";
import { ESP32Status } from "@/components/admin/esp32-status";
import { AnalyticsDashboard } from "@/components/admin/analytics-dashboard";
import { UserManagement } from "@/components/admin/user-management";
import { SupportDashboard } from "@/components/admin/support-dashboard";
import { LockerStatusManagement } from "@/components/admin/locker-status-management";
import { AdminLogin } from "@/components/admin/admin-login";
import { SystemSettings } from "@/components/admin/system-settings";
import { AuditTrail } from "@/components/admin/audit-trail";
import { AlertRules } from "@/components/admin/alert-rules";
import { ScheduledReports } from "@/components/admin/scheduled-reports";
import { ReportBuilder } from "@/components/admin/report-builder";
import { BulkUserOperations } from "@/components/admin/bulk-user-operations";
import { PredictiveMaintenance } from "@/components/admin/predictive-maintenance";
import { IntegrationManagement } from "@/components/admin/integration-management";
import { ApiMonitoring } from "@/components/admin/api-monitoring";
import { FeatureManagement } from "@/components/admin/feature-management";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import { useFeatureFlags } from "@/hooks/useFeatureFlags";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState("dashboard");
  const { adminUser, isLoading, isAuthenticated, login, logout } = useAdminAuth();
  const { isFeatureEnabled } = useFeatureFlags();

  // Get ESP32 connection status
  const { data: connectedDevices } = useQuery({
    queryKey: ["/api/esp32/connected"],
    refetchInterval: 10000,
    initialData: { count: 0, devices: [] }
  });

  // Show login page if not authenticated
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading admin portal...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !adminUser) {
    return <AdminLogin onLogin={(user, token) => login(user, token)} />;
  }

  // Helper function to check if user can access a feature
  const canAccessFeature = (featureName: string, requiresCreator = false): boolean => {
    if (requiresCreator && adminUser?.role !== 'creator') {
      return false;
    }
    return isFeatureEnabled(featureName as any);
  };

  // Component to show when access is denied
  const AccessDenied = ({ featureName }: { featureName: string }) => (
    <div className="flex flex-col items-center justify-center h-64 text-center">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Feature Not Available</h3>
      <p className="text-gray-600 max-w-md">
        The {featureName} feature is currently disabled. Contact your system administrator for access.
      </p>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        return <Dashboard />;
      case "lockers":
        return <LockersGrid />;
      case "locker-status":
        return <LockerStatusManagement />;
      case "user-management":
        return <UserManagement />;
      case "support":
        return <SupportDashboard currentAdmin={adminUser} />;
      case "alerts":
        return <ActivityFeed />;
      case "esp32":
        return <ESP32Status />;
      case "analytics":
        return <AnalyticsDashboard />;
      case "alert-rules":
        return canAccessFeature("alert_rules") ? <AlertRules /> : <AccessDenied featureName="Alert Rules" />;
      case "scheduled-reports":
        return canAccessFeature("scheduled_reports") ? <ScheduledReports /> : <AccessDenied featureName="Scheduled Reports" />;
      case "report-builder":
        return canAccessFeature("report_builder") ? <ReportBuilder /> : <AccessDenied featureName="Report Builder" />;
      case "bulk-user-operations":
        return canAccessFeature("bulk_user_operations") ? <BulkUserOperations /> : <AccessDenied featureName="Bulk User Operations" />;
      case "predictive-maintenance":
        return canAccessFeature("predictive_maintenance") ? <PredictiveMaintenance /> : <AccessDenied featureName="Predictive Maintenance" />;
      case "integration-management":
        return canAccessFeature("integrations") ? <IntegrationManagement /> : <AccessDenied featureName="Integrations" />;
      case "api-monitoring":
        return canAccessFeature("api_monitoring") ? <ApiMonitoring /> : <AccessDenied featureName="API Monitoring" />;
      case "system-settings":
        if (adminUser?.role === 'creator') {
          return <FeatureManagement />;
        } else if (canAccessFeature("system_settings")) {
          return <SystemSettings />;
        } else {
          return <AccessDenied featureName="System Settings" />;
        }
      case "audit-trail":
        return canAccessFeature("audit_trail") ? <AuditTrail /> : <AccessDenied featureName="Audit Trail" />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar activeSection={activeSection} onSectionChange={setActiveSection} />
      <div className="flex-1 lg:ml-64">
        <header className="bg-white shadow-sm border-b">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">SwiSto Admin</h1>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    connectedDevices?.count > 0
                      ? 'bg-green-500 animate-pulse'
                      : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm text-gray-600">
                    ESP32 {connectedDevices?.count > 0 ? 'Connected' : 'Offline'}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-sm text-gray-700">
                    Welcome, {adminUser.firstName} {adminUser.lastName}!
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={logout}
                    className="flex items-center space-x-1"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </header>
        <main className="p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
