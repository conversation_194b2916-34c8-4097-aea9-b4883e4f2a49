import { useQuery } from "@tanstack/react-query";

export interface FeatureFlag {
  enabled: boolean;
  description: string;
  isEditable: boolean;
}

export interface FeatureFlags {
  alert_rules: FeatureFlag;
  scheduled_reports: FeatureFlag;
  report_builder: FeatureFlag;
  bulk_user_operations: FeatureFlag;
  predictive_maintenance: FeatureFlag;
  integrations: FeatureFlag;
  api_monitoring: FeatureFlag;
  system_settings: FeatureFlag;
  audit_trail: FeatureFlag;
}

export function useFeatureFlags() {
  const { data: features, isLoading, error } = useQuery<FeatureFlags>({
    queryKey: ["/api/admin/system-settings/features"],
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Helper function to check if a feature is enabled
  const isFeatureEnabled = (featureName: keyof FeatureFlags): boolean => {
    return features?.[featureName]?.enabled ?? false;
  };

  // Helper function to get all enabled features
  const getEnabledFeatures = (): string[] => {
    if (!features) return [];
    return Object.entries(features)
      .filter(([_, flag]) => flag.enabled)
      .map(([name]) => name);
  };

  // Helper function to get all disabled features
  const getDisabledFeatures = (): string[] => {
    if (!features) return [];
    return Object.entries(features)
      .filter(([_, flag]) => !flag.enabled)
      .map(([name]) => name);
  };

  return {
    features,
    isLoading,
    error,
    isFeatureEnabled,
    getEnabledFeatures,
    getDisabledFeatures,
  };
}
