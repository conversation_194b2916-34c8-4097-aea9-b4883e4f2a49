{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx scripts/startup-prompt.js", "dev:local": "tsx scripts/database-prompt.js && cross-env NODE_ENV=development tsx server/index.ts", "dev:external": "tsx scripts/database-prompt.js && tsx scripts/external-access.js", "dev:direct": "cross-env NODE_ENV=development tsx server/index.ts", "dev:esp32": "cross-env NODE_ENV=development ENABLE_ESP32=true tsx server/index.ts", "dev:esp32-usb": "cross-env NODE_ENV=development ENABLE_ESP32=true ESP32_MODE=usb tsx server/index.ts", "dev:esp32-wifi": "cross-env NODE_ENV=development ENABLE_ESP32=true ESP32_MODE=wifi tsx server/index.ts", "dev:no-esp32": "cross-env NODE_ENV=development ENABLE_ESP32=false tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:init": "tsx scripts/init-db.js", "db:empty": "tsx scripts/init-db.js", "db:reset": "npm run db:empty", "db:prompt": "tsx scripts/database-prompt.js", "network": "tsx scripts/network-info.ts", "tunnel": "tsx scripts/external-access.js", "setup:ngrok": "tsx scripts/setup-ngrok.js", "setup:mobile": "tsx scripts/setup-mobile-apps.js", "stop": "tsx scripts/stop-server.js", "test:windows": "tsx scripts/test-windows-compatibility.js", "find:esp32": "tsx scripts/find-esp32-devices.js", "migrate": "tsx server/scripts/migrate.ts", "migrate:status": "tsx server/scripts/migrate.ts status", "migrate:rollback": "tsx server/scripts/migrate.ts rollback", "create:creator": "tsx scripts/create-creator.js", "build:mobile": "npm run build && npx cap sync", "dev:mobile": "npm run dev & npx cap run android --livereload --external", "build:android": "npm run build && npx cap sync android && npx cap build android", "build:ios": "npm run build && npx cap sync ios && npx cap build ios", "open:android": "npx cap open android", "open:ios": "npx cap open ios", "sync:mobile": "npx cap sync"}, "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.1", "@capacitor/geolocation": "^7.1.2", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.3.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/local-notifications": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.60.5", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.13", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.8.0", "connect-sqlite3": "^0.9.15", "cors": "^2.8.5", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.1", "next-themes": "^0.4.6", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "openid-client": "^6.5.0", "package.json": "^0.0.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-leaflet": "^4.2.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "serialport": "^13.0.0", "stripe": "^18.2.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.2", "xss-clean": "^0.1.4", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/better-sqlite3": "^7.6.13", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/leaflet": "^1.9.18", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "drizzle-kit": "^0.18.1", "esbuild": "^0.25.0", "fkill": "^9.0.0", "get-port": "^7.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^6.3.5", "vite-plugin-error-overlay": "^0.0.1-alpha.0"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}