#include <ArduinoJson.h>
#include <HX711.h>
#include "esp_camera.h"
#include <base64.h>

// ----------- <PERSON><PERSON><PERSON><PERSON><PERSON> PIN CONFIGURATION ----------
// Camera pins for ESP32-CAM (AI-Thinker model)
#define PWDN_GPIO_NUM     32
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM      0
#define SIOD_GPIO_NUM     26
#define SIOC_GPIO_NUM     27
#define Y9_GPIO_NUM       35
#define Y8_GPIO_NUM       34
#define Y7_GPIO_NUM       39
#define Y6_GPIO_NUM       36
#define Y5_GPIO_NUM       21
#define Y4_GPIO_NUM       19
#define Y3_GPIO_NUM       18
#define Y2_GPIO_NUM        5
#define VSYNC_GPIO_NUM    25
#define HREF_GPIO_NUM     23
#define PCLK_GPIO_NUM     22

// Weight sensor pins
#define WEIGHT_DOUT_PIN   4
#define WEIGHT_SCK_PIN    2

// Lock control pins
#define LOCK_RELAY_PIN    12
#define DOOR_SENSOR_PIN   13
#define RGB_RED_PIN       14
#define RGB_GREEN_PIN     15
#define RGB_BLUE_PIN      16

// ----------- DEVICE CONFIGURATION ----------
String DEVICE_ID = "ESP32_USB_001";  // Unique identifier for this device
String LOCKER_ID = "1";              // Locker ID this device controls
String FIRMWARE_VERSION = "2.0.0-USB";
String DEVICE_TYPE = "ESP32_LOCKER_CONTROLLER_USB";

// ----------- HARDWARE OBJECTS ----------
HX711 scale;

// ----------- SYSTEM STATE ----------
struct SystemState {
  bool systemReady = false;
  bool doorOpen = false;
  bool isLocked = true;
  float currentWeight = 0.0;
  int batteryLevel = 100; // Simulated for USB power
  unsigned long lastHeartbeat = 0;
  unsigned long lastSensorRead = 0;
  bool cameraInitialized = false;
} state;

// ----------- COMMUNICATION SETTINGS ----------
const unsigned long HEARTBEAT_INTERVAL = 30000;  // 30 seconds
const unsigned long SENSOR_READ_INTERVAL = 5000; // 5 seconds
const int SERIAL_BAUD_RATE = 115200;

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);
  delay(1000);
  
  Serial.println("=== ESP32 Smart Locker Controller (USB Mode) Starting ===");
  
  // Initialize hardware
  initializePins();
  initializeCamera();
  initializeWeightSensor();
  
  // Send identification to server
  sendIdentification();
  
  // Set initial state
  setRGBColor(255, 255, 0); // Yellow - initializing
  state.systemReady = true;
  setRGBColor(0, 255, 0);   // Green - ready
  
  Serial.println("=== System Ready (USB Mode) ===");
  Serial.println("Waiting for commands from server...");
}

void loop() {
  // Handle incoming serial commands
  handleSerialCommands();
  
  // Read sensors periodically
  if (millis() - state.lastSensorRead > SENSOR_READ_INTERVAL) {
    readSensors();
    state.lastSensorRead = millis();
  }
  
  // Send heartbeat periodically
  if (millis() - state.lastHeartbeat > HEARTBEAT_INTERVAL) {
    sendHeartbeat();
    state.lastHeartbeat = millis();
  }
  
  delay(100); // Small delay to prevent overwhelming the serial
}

void initializePins() {
  Serial.println("Initializing GPIO pins...");
  
  // Lock control
  pinMode(LOCK_RELAY_PIN, OUTPUT);
  digitalWrite(LOCK_RELAY_PIN, HIGH); // Locked by default
  
  // Door sensor
  pinMode(DOOR_SENSOR_PIN, INPUT_PULLUP);
  
  // RGB LED
  pinMode(RGB_RED_PIN, OUTPUT);
  pinMode(RGB_GREEN_PIN, OUTPUT);
  pinMode(RGB_BLUE_PIN, OUTPUT);
  
  Serial.println("✅ GPIO pins initialized");
}

void initializeCamera() {
  Serial.println("Initializing camera...");
  
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sscb_sda = SIOD_GPIO_NUM;
  config.pin_sscb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;
  config.pixel_format = PIXFORMAT_JPEG;
  
  if (psramFound()) {
    config.frame_size = FRAMESIZE_UXGA;
    config.jpeg_quality = 10;
    config.fb_count = 2;
  } else {
    config.frame_size = FRAMESIZE_SVGA;
    config.jpeg_quality = 12;
    config.fb_count = 1;
  }
  
  esp_err_t err = esp_camera_init(&config);
  if (err != ESP_OK) {
    Serial.printf("❌ Camera init failed with error 0x%x\n", err);
    state.cameraInitialized = false;
  } else {
    Serial.println("✅ Camera initialized successfully");
    state.cameraInitialized = true;
  }
}

void initializeWeightSensor() {
  Serial.println("Initializing weight sensor...");
  
  scale.begin(WEIGHT_DOUT_PIN, WEIGHT_SCK_PIN);
  
  if (scale.is_ready()) {
    scale.set_scale(2280.f); // Calibration factor (adjust as needed)
    scale.tare();            // Reset to zero
    Serial.println("✅ Weight sensor initialized and calibrated");
  } else {
    Serial.println("❌ Weight sensor not found");
  }
}

void handleSerialCommands() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    
    if (command.length() > 0) {
      processCommand(command);
    }
  }
}

void processCommand(String commandStr) {
  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, commandStr);
  
  if (error) {
    Serial.println("❌ Failed to parse command JSON");
    return;
  }
  
  String type = doc["type"];
  String command = doc["command"];
  
  if (type == "identify") {
    sendIdentification();
  }
  else if (type == "command") {
    executeCommand(command, doc);
  }
  else if (type == "welcome") {
    Serial.println("📡 Connected to SwiSto server successfully");
  }
  else {
    Serial.println("❓ Unknown command type: " + type);
  }
}

void executeCommand(String command, DynamicJsonDocument& doc) {
  bool success = false;
  String error = "";
  
  if (command == "open") {
    success = openLocker();
    if (!success) error = "Failed to open locker";
  }
  else if (command == "close") {
    success = closeLocker();
    if (!success) error = "Failed to close locker";
  }
  else if (command == "lock") {
    success = lockLocker();
    if (!success) error = "Failed to lock locker";
  }
  else if (command == "unlock") {
    success = unlockLocker();
    if (!success) error = "Failed to unlock locker";
  }
  else if (command == "capture_image") {
    success = captureImage();
    if (!success) error = "Failed to capture image";
  }
  else if (command == "status") {
    sendStatusUpdate();
    success = true;
  }
  else {
    error = "Unknown command: " + command;
  }
  
  // Send command response
  sendCommandResponse(command, success, error);
}

bool openLocker() {
  if (!state.isLocked) {
    Serial.println("🔓 Locker is already unlocked");
    return true;
  }
  
  Serial.println("🔓 Opening locker...");
  digitalWrite(LOCK_RELAY_PIN, LOW); // Unlock
  state.isLocked = false;
  setRGBColor(0, 0, 255); // Blue - unlocked
  
  return true;
}

bool closeLocker() {
  Serial.println("🔒 Closing locker...");
  digitalWrite(LOCK_RELAY_PIN, HIGH); // Lock
  state.isLocked = true;
  setRGBColor(255, 0, 0); // Red - locked
  
  return true;
}

bool lockLocker() {
  return closeLocker();
}

bool unlockLocker() {
  return openLocker();
}

bool captureImage() {
  if (!state.cameraInitialized) {
    Serial.println("❌ Camera not initialized");
    return false;
  }
  
  Serial.println("📸 Capturing image...");
  
  camera_fb_t* fb = esp_camera_fb_get();
  if (!fb) {
    Serial.println("❌ Camera capture failed");
    return false;
  }
  
  // Convert image to base64 and send in chunks
  String imageData = base64::encode(fb->buf, fb->len);
  sendImageInChunks(imageData);
  
  esp_camera_fb_return(fb);
  Serial.println("✅ Image captured and sent");
  
  return true;
}

void sendImageInChunks(String imageData) {
  const int chunkSize = 1000; // Send in 1KB chunks
  int totalChunks = (imageData.length() + chunkSize - 1) / chunkSize;
  String imageId = String(millis()); // Simple image ID
  
  for (int i = 0; i < totalChunks; i++) {
    int start = i * chunkSize;
    int end = min(start + chunkSize, (int)imageData.length());
    String chunk = imageData.substring(start, end);
    
    DynamicJsonDocument doc(1500);
    doc["type"] = "image_chunk";
    doc["deviceId"] = DEVICE_ID;
    doc["lockerId"] = LOCKER_ID;
    doc["imageId"] = imageId;
    doc["chunkIndex"] = i;
    doc["totalChunks"] = totalChunks;
    doc["data"] = chunk;
    
    String output;
    serializeJson(doc, output);
    Serial.println(output);
    
    delay(50); // Small delay between chunks
  }
}

void readSensors() {
  // Read weight sensor
  if (scale.is_ready()) {
    float weight = scale.get_units(3); // Average of 3 readings
    if (weight < 0) weight = 0; // Ignore negative weights
    state.currentWeight = weight;
  }
  
  // Read door sensor
  bool doorOpen = digitalRead(DOOR_SENSOR_PIN) == LOW;
  if (doorOpen != state.doorOpen) {
    state.doorOpen = doorOpen;
    Serial.println(doorOpen ? "🚪 Door opened" : "🚪 Door closed");
  }
  
  // Send sensor data
  sendSensorData();
}

void sendSensorData() {
  DynamicJsonDocument doc(512);
  doc["type"] = "sensor_data";
  doc["deviceId"] = DEVICE_ID;
  doc["lockerId"] = LOCKER_ID;
  doc["weight"] = state.currentWeight;
  doc["doorOpen"] = state.doorOpen;
  doc["isLocked"] = state.isLocked;
  doc["timestamp"] = millis();
  
  String output;
  serializeJson(doc, output);
  Serial.println(output);
}

void sendIdentification() {
  DynamicJsonDocument doc(512);
  doc["type"] = "identify_response";
  doc["deviceId"] = DEVICE_ID;
  doc["lockerId"] = LOCKER_ID;
  doc["firmwareVersion"] = FIRMWARE_VERSION;
  doc["deviceType"] = DEVICE_TYPE;
  doc["cameraAvailable"] = state.cameraInitialized;
  doc["timestamp"] = millis();
  
  String output;
  serializeJson(doc, output);
  Serial.println(output);
}

void sendHeartbeat() {
  DynamicJsonDocument doc(512);
  doc["type"] = "status_update";
  doc["deviceId"] = DEVICE_ID;
  doc["lockerId"] = LOCKER_ID;
  doc["status"] = state.systemReady ? "online" : "offline";
  doc["batteryLevel"] = state.batteryLevel;
  doc["weight"] = state.currentWeight;
  doc["isLocked"] = state.isLocked;
  doc["doorOpen"] = state.doorOpen;
  doc["timestamp"] = millis();
  
  String output;
  serializeJson(doc, output);
  Serial.println(output);
}

void sendStatusUpdate() {
  sendHeartbeat(); // Same as heartbeat
}

void sendCommandResponse(String command, bool success, String error) {
  DynamicJsonDocument doc(512);
  doc["type"] = "command_response";
  doc["deviceId"] = DEVICE_ID;
  doc["lockerId"] = LOCKER_ID;
  doc["command"] = command;
  doc["success"] = success;
  if (!success) {
    doc["error"] = error;
  }
  doc["timestamp"] = millis();
  
  String output;
  serializeJson(doc, output);
  Serial.println(output);
}

void setRGBColor(int red, int green, int blue) {
  analogWrite(RGB_RED_PIN, red);
  analogWrite(RGB_GREEN_PIN, green);
  analogWrite(RGB_BLUE_PIN, blue);
}
