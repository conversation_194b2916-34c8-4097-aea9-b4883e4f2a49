# ESP32 USB Connection Setup Guide

This guide explains how to set up ESP32 devices to communicate with the SwiSto server via USB serial connection instead of WiFi.

## 🔌 USB vs WiFi Comparison

| Feature | WiFi Mode | USB Mode |
|---------|-----------|----------|
| **Connection** | Wireless network | Direct USB cable |
| **Setup Complexity** | Medium (WiFi config) | Simple (plug & play) |
| **Range** | Limited by WiFi range | Limited by USB cable |
| **Power** | Battery/External | USB powered |
| **Reliability** | Network dependent | Very stable |
| **Security** | Network security | Physical security |
| **Multiple Devices** | Easy (network) | Multiple USB ports needed |

## 🚀 Quick Start

### 1. Server Configuration

Choose your ESP32 connection mode by setting environment variables:

**For USB Mode:**
```bash
npm run dev:esp32-usb
```

**For WiFi Mode:**
```bash
npm run dev:esp32-wifi
```

**Environment Variables:**
```bash
# Enable ESP32 integration
ENABLE_ESP32=true

# Connection mode: 'wifi' or 'usb'
ESP32_MODE=usb

# USB Serial settings
ESP32_BAUD_RATE=115200
ESP32_AUTO_DETECT=true
```

### 2. ESP32 Firmware

**For USB Mode:**
- Upload `esp32_locker_controller_usb.ino` to your ESP32
- No WiFi configuration needed
- Connect via USB cable

**For WiFi Mode:**
- Upload `esp32_locker_controller.ino` to your ESP32
- Configure WiFi credentials
- Connect to same network as server

## 📋 USB Mode Setup Steps

### Step 1: Prepare ESP32 Hardware

1. **ESP32-CAM Module** (recommended)
   - Built-in camera for image capture
   - Multiple GPIO pins for sensors
   - USB-to-serial adapter required

2. **Required Components:**
   - ESP32-CAM board
   - USB-to-serial adapter (CP2102 or CH340)
   - HX711 weight sensor module
   - Load cell (weight sensor)
   - Relay module for lock control
   - RGB LED for status indication
   - Door sensor (magnetic switch)

### Step 2: Hardware Connections

```
ESP32-CAM Pin Connections:
├── Weight Sensor (HX711)
│   ├── DOUT → GPIO 4
│   └── SCK  → GPIO 2
├── Lock Control
│   └── Relay → GPIO 12
├── Door Sensor
│   └── Switch → GPIO 13
├── Status LED (RGB)
│   ├── Red   → GPIO 14
│   ├── Green → GPIO 15
│   └── Blue  → GPIO 16
└── USB Serial
    ├── TX → RX (adapter)
    ├── RX → TX (adapter)
    └── GND → GND
```

### Step 3: Upload Firmware

1. **Install Arduino IDE** with ESP32 support
2. **Install Required Libraries:**
   ```
   - ArduinoJson (by Benoit Blanchon)
   - HX711 (by Bogdan Necula)
   - ESP32 Camera (built-in)
   - Base64 (by Densaugeo)
   ```

3. **Configure Device Settings** in firmware:
   ```cpp
   String DEVICE_ID = "ESP32_USB_001";  // Unique device ID
   String LOCKER_ID = "1";              // Locker number
   ```

4. **Upload Firmware:**
   - Select Board: "AI Thinker ESP32-CAM"
   - Select Port: Your USB serial port
   - Upload `esp32_locker_controller_usb.ino`

### Step 4: Connect to Server

1. **Start Server in USB Mode:**
   ```bash
   npm run dev:esp32-usb
   ```

2. **Connect ESP32 via USB:**
   - Plug ESP32 into computer USB port
   - Server will auto-detect the device
   - Check console for connection messages

3. **Verify Connection:**
   - Look for "Device registered" messages
   - Check admin panel for connected devices
   - Test locker commands

## 🔧 Configuration Options

### Environment Variables

```bash
# Basic ESP32 settings
ENABLE_ESP32=true           # Enable ESP32 integration
ESP32_MODE=usb              # Connection mode: 'wifi' or 'usb'

# USB Serial settings
ESP32_BAUD_RATE=115200      # Serial communication speed
ESP32_AUTO_DETECT=true      # Auto-detect ESP32 devices
```

### Device Configuration

Edit these values in the ESP32 firmware:

```cpp
// Device identification
String DEVICE_ID = "ESP32_USB_001";     // Unique device identifier
String LOCKER_ID = "1";                 // Locker ID this device controls
String FIRMWARE_VERSION = "2.0.0-USB"; // Firmware version

// Communication settings
const int SERIAL_BAUD_RATE = 115200;    // Must match server setting
const unsigned long HEARTBEAT_INTERVAL = 30000;  // 30 seconds
const unsigned long SENSOR_READ_INTERVAL = 5000; // 5 seconds
```

## 🔍 Troubleshooting

### Common Issues

**1. Device Not Detected**
```bash
# Check if device is connected
ls /dev/ttyUSB* /dev/ttyACM*  # Linux
ls /dev/cu.*                  # macOS

# Check server logs
npm run dev:esp32-usb
# Look for "ESP32 device detected" messages
```

**2. Permission Denied (Linux)**
```bash
# Add user to dialout group
sudo usermod -a -G dialout $USER
# Logout and login again

# Or set permissions
sudo chmod 666 /dev/ttyUSB0
```

**3. Communication Errors**
- Check baud rate matches (115200)
- Verify USB cable supports data (not just power)
- Try different USB port
- Check ESP32 power supply

**4. Multiple Devices**
- Each device needs unique DEVICE_ID
- Use USB hub for multiple connections
- Check server logs for device registration

### Debug Commands

**Test Serial Communication:**
```bash
# Install screen or minicom
sudo apt install screen

# Connect to ESP32 directly
screen /dev/ttyUSB0 115200

# Send test command
{"type":"identify"}
```

**Server Debug Mode:**
```bash
# Enable verbose logging
DEBUG=esp32:* npm run dev:esp32-usb
```

## 📊 Monitoring

### Admin Panel Features

1. **Device Status:**
   - Connected USB devices
   - Connection type (USB/WiFi)
   - Last communication time
   - Battery level (simulated for USB)

2. **Real-time Data:**
   - Weight sensor readings
   - Door open/closed status
   - Lock status
   - Camera availability

3. **Commands:**
   - Open/close locker
   - Capture image
   - Get status update
   - Emergency unlock

### API Endpoints

```bash
# Get connected devices
GET /api/esp32/devices

# Send command to locker
POST /api/esp32/command
{
  "lockerId": "1",
  "command": "open"
}

# Get locker status
GET /api/esp32/status/1
```

## 🔒 Security Considerations

### USB Mode Security

**Advantages:**
- Physical access required
- No network vulnerabilities
- Direct communication
- No WiFi credentials needed

**Considerations:**
- Secure physical access to USB ports
- Use quality USB cables
- Monitor for unauthorized devices
- Regular firmware updates

### Best Practices

1. **Device Management:**
   - Use unique device IDs
   - Regular firmware updates
   - Monitor connection logs
   - Secure physical access

2. **Data Protection:**
   - Encrypt sensitive commands
   - Validate device identity
   - Log all communications
   - Regular security audits

## 🚀 Production Deployment

### Scaling USB Connections

**Single Computer:**
- Use USB hubs (powered recommended)
- Maximum ~127 devices per USB controller
- Consider USB 3.0 for better performance

**Multiple Computers:**
- Distribute devices across machines
- Use network API for coordination
- Centralized database management

### Reliability

**Hardware:**
- Use industrial USB cables
- Powered USB hubs for stability
- Backup power for ESP32 devices
- Regular hardware maintenance

**Software:**
- Auto-reconnection on disconnect
- Device health monitoring
- Error logging and alerts
- Graceful failure handling

## 📞 Support

For issues with USB ESP32 integration:

1. Check this documentation
2. Review server logs
3. Test with single device first
4. Verify hardware connections
5. Update firmware if needed

**Common Log Messages:**
- `✅ ESP32 device detected` - Device found
- `📋 Registering ESP32 device` - Device connecting
- `❌ Serial port error` - Connection issue
- `🔄 Attempting to reconnect` - Auto-recovery
