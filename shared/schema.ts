import { sqliteTable, text, integer, real, index } from "drizzle-orm/sqlite-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations, sql } from "drizzle-orm";

// Session storage table for authentication
export const sessions = sqliteTable(
  "sessions",
  {
    sid: text("sid").primaryKey(),
    sess: text("sess").notNull(), // JSON stored as text
    expire: integer("expire").notNull(), // Unix timestamp
  },
  (table) => ({
    expireIdx: index("IDX_session_expire").on(table.expire),
  }),
);

// Users table with enhanced role management
export const users = sqliteTable("users", {
  id: text("id").primaryKey().notNull(),
  email: text("email").unique().notNull(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  profileImageUrl: text("profile_image_url"),
  role: text("role").notNull().default("user"), // user, agent, admin, creator
  status: text("status").notNull().default("active"), // active, inactive, suspended, restricted
  phoneNumber: text("phone_number"),
  username: text("username").unique(),
  password: text("password"),
  verificationStatus: integer("verification_status", { mode: 'boolean' }).default(false),
  // Premium subscription fields
  subscriptionType: text("subscription_type").default("basic"), // basic, premium, premium_plus
  subscriptionStatus: text("subscription_status").default("active"), // active, cancelled, expired, trial
  subscriptionStartDate: integer("subscription_start_date"),
  subscriptionEndDate: integer("subscription_end_date"),
  trialEndDate: integer("trial_end_date"),
  paymentMethod: text("payment_method"), // credit_card, paypal, etc.
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  restrictionReason: text("restriction_reason"), // Reason for restriction
  restrictedAt: integer("restricted_at"), // Unix timestamp when user was restricted
  restrictedBy: text("restricted_by"), // Admin ID who restricted the user
  lastLoginAt: integer("last_login_at"), // Unix timestamp
  createdAt: integer("created_at").default(sql`(strftime('%s', 'now') * 1000)`), // Unix timestamp
  updatedAt: integer("updated_at").default(sql`(strftime('%s', 'now') * 1000)`), // Unix timestamp
});

// Locations table with additional metadata
export const locations = sqliteTable("locations", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  address: text("address").notNull(),
  city: text("city"),
  state: text("state"),
  zipCode: text("zip_code"),
  country: text("country").default("US"),
  latitude: real("latitude"),
  longitude: real("longitude"),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  capacity: integer("capacity"),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  coordinatesIdx: index("idx_location_coordinates").on(table.latitude, table.longitude),
}));

// Lockers table with enhanced tracking
export const lockers = sqliteTable("lockers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  size: text("size").notNull(), // small, medium, large
  status: text("status").notNull().default("available"), // available, occupied, maintenance, expired, unlocked
  locationId: integer("location_id").references(() => locations.id),
  esp32Id: text("esp32_id"),
  lastPing: integer("last_ping"), // Unix timestamp for ESP32 last ping
  lastMaintenanceDate: integer("last_maintenance_date"), // Unix timestamp
  maintenanceInterval: integer("maintenance_interval"), // days between maintenance
  temperatureSensor: integer("temperature_sensor", { mode: 'boolean' }).default(false),
  humiditySensor: integer("humidity_sensor", { mode: 'boolean' }).default(false),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  statusIdx: index("idx_locker_status").on(table.status),
  locationIdx: index("idx_locker_location").on(table.locationId),
}));

// Bookings table with enhanced payment tracking
export const bookings = sqliteTable("bookings", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: text("user_id").notNull().references(() => users.id),
  lockerId: integer("locker_id").notNull().references(() => lockers.id),
  agentId: text("agent_id").references(() => users.id),
  status: text("status").notNull().default("pending"), // pending, active, completed, cancelled
  startTime: integer("start_time").notNull(), // Unix timestamp
  endTime: integer("end_time"), // Unix timestamp
  duration: integer("duration"), // in minutes
  totalCost: real("total_cost"),
  paymentStatus: text("payment_status").notNull().default("simulated_paid"), // simulated_paid, simulated_pending, paid, pending, failed
  paymentMethod: text("payment_method").default("demo_card"), // demo_card, credit_card, paypal, etc.
  paymentId: text("payment_id"), // external payment reference
  notes: text("notes"),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  userIdx: index("idx_booking_user").on(table.userId),
  lockerIdx: index("idx_booking_locker").on(table.lockerId),
  statusIdx: index("idx_booking_status").on(table.status),
  datesIdx: index("idx_booking_dates").on(table.startTime, table.endTime),
}));

// Tasks table for agents with enhanced tracking
export const tasks = sqliteTable("tasks", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  agentId: text("agent_id").notNull().references(() => users.id),
  bookingId: integer("booking_id").references(() => bookings.id),
  type: text("type").notNull(), // delivery, pickup, maintenance
  description: text("description"),
  status: text("status").notNull().default("pending"), // pending, in_progress, completed, cancelled
  priority: text("priority").default("medium"), // low, medium, high, urgent
  estimatedTime: integer("estimated_time"), // in minutes
  actualTime: integer("actual_time"), // in minutes
  startTime: integer("start_time"), // Unix timestamp
  completionTime: integer("completion_time"), // Unix timestamp
  earnings: real("earnings"),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  agentIdx: index("idx_task_agent").on(table.agentId),
  bookingIdx: index("idx_task_booking").on(table.bookingId),
  statusIdx: index("idx_task_status").on(table.status),
  typeIdx: index("idx_task_type").on(table.type),
}));

// Activities table for comprehensive logging
export const activities = sqliteTable("activities", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  type: text("type").notNull(), // access, maintenance, alert, booking, registration, payment
  message: text("message").notNull(),
  lockerId: integer("locker_id").references(() => lockers.id),
  userId: text("user_id").references(() => users.id),
  bookingId: integer("booking_id").references(() => bookings.id),
  taskId: integer("task_id").references(() => tasks.id),
  severity: text("severity").default("info"), // info, warning, error, critical
  metadata: text("metadata"), // JSON stored as text
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  typeIdx: index("idx_activity_type").on(table.type),
  lockerIdx: index("idx_activity_locker").on(table.lockerId),
  userIdx: index("idx_activity_user").on(table.userId),
  createdIdx: index("idx_activity_created").on(table.createdAt),
}));

// Chat messages table with enhanced organization
export const chatMessages = sqliteTable("chat_messages", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  fromUserId: text("from_user_id").notNull().references(() => users.id),
  toUserId: text("to_user_id").notNull().references(() => users.id),
  bookingId: integer("booking_id").references(() => bookings.id),
  message: text("message").notNull(),
  isRead: integer("is_read", { mode: 'boolean' }).default(false),
  readAt: integer("read_at"), // Unix timestamp
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  fromUserIdx: index("idx_chat_from_user").on(table.fromUserId),
  toUserIdx: index("idx_chat_to_user").on(table.toUserId),
  bookingIdx: index("idx_chat_booking").on(table.bookingId),
  createdIdx: index("idx_chat_created").on(table.createdAt),
}));

// New table: Sensor readings for IoT data
export const sensorReadings = sqliteTable("sensor_readings", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  lockerId: integer("locker_id").notNull().references(() => lockers.id),
  type: text("type").notNull(), // temperature, humidity, motion, etc.
  value: real("value").notNull(),
  unit: text("unit").notNull(), // C, %, etc.
  timestamp: integer("timestamp").default(Date.now()),
}, (table) => ({
  lockerIdx: index("idx_sensor_locker").on(table.lockerId),
  typeIdx: index("idx_sensor_type").on(table.type),
  timestampIdx: index("idx_sensor_timestamp").on(table.timestamp),
}));

// New table: Maintenance records
export const maintenanceRecords = sqliteTable("maintenance_records", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  lockerId: integer("locker_id").notNull().references(() => lockers.id),
  technicianId: text("technician_id").references(() => users.id),
  type: text("type").notNull(), // routine, repair, inspection
  description: text("description").notNull(),
  status: text("status").notNull().default("scheduled"), // scheduled, in_progress, completed
  scheduledDate: integer("scheduled_date"), // Unix timestamp
  completedDate: integer("completed_date"), // Unix timestamp
  notes: text("notes"),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  lockerIdx: index("idx_maintenance_locker").on(table.lockerId),
  statusIdx: index("idx_maintenance_status").on(table.status),
}));

// New table: Swift Agent biometric credentials
export const agentBiometrics = sqliteTable("agent_biometrics", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  agentId: text("agent_id").notNull().references(() => users.id),
  biometricType: text("biometric_type").notNull(), // fingerprint, face_id, iris
  biometricData: text("biometric_data").notNull(), // encrypted biometric template
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  lastUsed: integer("last_used"), // Unix timestamp
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  agentIdx: index("idx_biometric_agent").on(table.agentId),
  typeIdx: index("idx_biometric_type").on(table.biometricType),
  activeIdx: index("idx_biometric_active").on(table.isActive),
}));

// New table: Locker access sessions for tracking active access periods
export const lockerAccessSessions = sqliteTable("locker_access_sessions", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  bookingId: integer("booking_id").notNull().references(() => bookings.id),
  userId: text("user_id").notNull().references(() => users.id),
  agentId: text("agent_id").references(() => users.id), // Swift Agent who accessed on behalf
  lockerId: integer("locker_id").notNull().references(() => lockers.id),
  accessMethod: text("access_method").notNull(), // biometric, pin, card, agent_biometric
  sessionStart: integer("session_start").notNull(), // Unix timestamp
  sessionEnd: integer("session_end"), // Unix timestamp - when access expires
  graceStart: integer("grace_start"), // Unix timestamp - when grace period starts
  graceEnd: integer("grace_end"), // Unix timestamp - when grace period ends
  status: text("status").notNull().default("active"), // active, expired, grace_period, revoked
  warningsSent: integer("warnings_sent").default(0), // count of expiration warnings sent
  lastWarning: integer("last_warning"), // Unix timestamp of last warning
  revokedAt: integer("revoked_at"), // Unix timestamp when access was revoked
  revokedBy: text("revoked_by"), // system, admin, user
  metadata: text("metadata"), // JSON stored as text for additional data
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  bookingIdx: index("idx_access_booking").on(table.bookingId),
  userIdx: index("idx_access_user").on(table.userId),
  agentIdx: index("idx_access_agent").on(table.agentId),
  lockerIdx: index("idx_access_locker").on(table.lockerId),
  statusIdx: index("idx_access_status").on(table.status),
  sessionIdx: index("idx_access_session").on(table.sessionStart, table.sessionEnd),
  graceIdx: index("idx_access_grace").on(table.graceStart, table.graceEnd),
}));

// New table: Expiration notifications for tracking sent notifications
export const expirationNotifications = sqliteTable("expiration_notifications", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  sessionId: integer("session_id").notNull().references(() => lockerAccessSessions.id),
  userId: text("user_id").notNull().references(() => users.id),
  agentId: text("agent_id").references(() => users.id),
  notificationType: text("notification_type").notNull(), // warning_15min, warning_5min, expired, grace_period, final_warning
  message: text("message").notNull(),
  sentAt: integer("sent_at").notNull(), // Unix timestamp
  deliveryMethod: text("delivery_method").notNull(), // websocket, push, email, sms
  deliveryStatus: text("delivery_status").notNull().default("sent"), // sent, delivered, failed
  acknowledged: integer("acknowledged", { mode: 'boolean' }).default(false),
  acknowledgedAt: integer("acknowledged_at"), // Unix timestamp
  metadata: text("metadata"), // JSON stored as text
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  sessionIdx: index("idx_notification_session").on(table.sessionId),
  userIdx: index("idx_notification_user").on(table.userId),
  agentIdx: index("idx_notification_agent").on(table.agentId),
  typeIdx: index("idx_notification_type").on(table.notificationType),
  sentIdx: index("idx_notification_sent").on(table.sentAt),
  statusIdx: index("idx_notification_status").on(table.deliveryStatus),
}));

// Subscription plans table
export const subscriptionPlans = sqliteTable("subscription_plans", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(), // basic, premium, premium_plus
  displayName: text("display_name").notNull(),
  description: text("description"),
  price: real("price").notNull(), // Monthly price
  yearlyPrice: real("yearly_price"), // Yearly price (with discount)
  currency: text("currency").notNull().default("USD"),
  features: text("features").notNull(), // JSON array of features
  maxBookingsPerMonth: integer("max_bookings_per_month"),
  maxConcurrentBookings: integer("max_concurrent_bookings"),
  prioritySupport: integer("priority_support", { mode: 'boolean' }).default(false),
  advancedFeatures: integer("advanced_features", { mode: 'boolean' }).default(false),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  sortOrder: integer("sort_order").default(0),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
});

// Subscription history table
export const subscriptionHistory = sqliteTable("subscription_history", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: text("user_id").notNull().references(() => users.id),
  planId: integer("plan_id").references(() => subscriptionPlans.id),
  action: text("action").notNull(), // subscribed, upgraded, downgraded, cancelled, renewed, expired
  fromPlan: text("from_plan"),
  toPlan: text("to_plan"),
  amount: real("amount"),
  currency: text("currency").default("USD"),
  paymentMethod: text("payment_method"),
  transactionId: text("transaction_id"),
  reason: text("reason"), // user_request, payment_failed, admin_action, etc.
  metadata: text("metadata"), // JSON for additional data
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  userIdx: index("idx_subscription_history_user").on(table.userId),
  actionIdx: index("idx_subscription_history_action").on(table.action),
  createdAtIdx: index("idx_subscription_history_created").on(table.createdAt),
}));

// System Settings table for global configuration
export const systemSettings = sqliteTable("system_settings", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  key: text("key").notNull().unique(),
  value: text("value").notNull(),
  type: text("type").notNull().default("string"), // string, number, boolean, json
  category: text("category").notNull(), // general, notifications, security, maintenance, api
  description: text("description"),
  isEditable: integer("is_editable", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  keyIdx: index("idx_settings_key").on(table.key),
  categoryIdx: index("idx_settings_category").on(table.category),
}));

// Audit Trail table for tracking admin actions
export const auditLogs = sqliteTable("audit_logs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: text("user_id").notNull().references(() => users.id),
  action: text("action").notNull(), // create, update, delete, login, logout, etc.
  resource: text("resource").notNull(), // user, locker, booking, system_setting, etc.
  resourceId: text("resource_id"), // ID of the affected resource
  oldValues: text("old_values"), // JSON of previous values
  newValues: text("new_values"), // JSON of new values
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  sessionId: text("session_id"),
  severity: text("severity").default("info"), // info, warning, error, critical
  metadata: text("metadata"), // Additional context as JSON
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  userIdx: index("idx_audit_user").on(table.userId),
  actionIdx: index("idx_audit_action").on(table.action),
  resourceIdx: index("idx_audit_resource").on(table.resource),
  createdIdx: index("idx_audit_created").on(table.createdAt),
}));

// Admin Sessions table for enhanced session management
export const adminSessions = sqliteTable("admin_sessions", {
  id: text("id").primaryKey(),
  userId: text("user_id").notNull().references(() => users.id),
  token: text("token").notNull().unique(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  lastActivity: integer("last_activity").default(Date.now()),
  expiresAt: integer("expires_at").notNull(),
  twoFactorVerified: integer("two_factor_verified", { mode: 'boolean' }).default(false),
  createdAt: integer("created_at").default(Date.now()),
}, (table) => ({
  userIdx: index("idx_admin_session_user").on(table.userId),
  tokenIdx: index("idx_admin_session_token").on(table.token),
  activeIdx: index("idx_admin_session_active").on(table.isActive),
  expiresIdx: index("idx_admin_session_expires").on(table.expiresAt),
}));

// Alert Rules table for custom alerting system
export const alertRules = sqliteTable("alert_rules", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  description: text("description"),
  condition: text("condition").notNull(), // JSON condition object
  threshold: real("threshold"),
  operator: text("operator").notNull(), // gt, lt, eq, gte, lte, contains
  metric: text("metric").notNull(), // occupancy_rate, response_time, error_rate, etc.
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  severity: text("severity").notNull().default("warning"), // info, warning, error, critical
  cooldownMinutes: integer("cooldown_minutes").default(60),
  lastTriggered: integer("last_triggered"),
  triggerCount: integer("trigger_count").default(0),
  notificationChannels: text("notification_channels"), // JSON array of channels
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  nameIdx: index("idx_alert_rule_name").on(table.name),
  metricIdx: index("idx_alert_rule_metric").on(table.metric),
  activeIdx: index("idx_alert_rule_active").on(table.isActive),
}));

// Scheduled Reports table
export const scheduledReports = sqliteTable("scheduled_reports", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  description: text("description"),
  reportType: text("report_type").notNull(), // usage, revenue, maintenance, user_activity
  schedule: text("schedule").notNull(), // cron expression
  recipients: text("recipients").notNull(), // JSON array of email addresses
  parameters: text("parameters"), // JSON report parameters
  format: text("format").notNull().default("pdf"), // pdf, csv, excel
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  lastRun: integer("last_run"),
  nextRun: integer("next_run"),
  runCount: integer("run_count").default(0),
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  typeIdx: index("idx_scheduled_report_type").on(table.reportType),
  activeIdx: index("idx_scheduled_report_active").on(table.isActive),
  nextRunIdx: index("idx_scheduled_report_next_run").on(table.nextRun),
}));

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  bookings: many(bookings, { relationName: "userBookings" }),
  agentBookings: many(bookings, { relationName: "agentBookings" }),
  tasks: many(tasks),
  activities: many(activities),
  sentMessages: many(chatMessages, { relationName: "sentMessages" }),
  receivedMessages: many(chatMessages, { relationName: "receivedMessages" }),
  maintenanceRecords: many(maintenanceRecords),
  biometrics: many(agentBiometrics),
  accessSessions: many(lockerAccessSessions, { relationName: "userAccessSessions" }),
  agentAccessSessions: many(lockerAccessSessions, { relationName: "agentAccessSessions" }),
  expirationNotifications: many(expirationNotifications, { relationName: "userNotifications" }),
  auditLogs: many(auditLogs),
  adminSessions: many(adminSessions),
  alertRules: many(alertRules),
  scheduledReports: many(scheduledReports),
  agentExpirationNotifications: many(expirationNotifications, { relationName: "agentNotifications" }),
  subscriptionHistory: many(subscriptionHistory),
}));

// Maintenance Schedules table for predictive maintenance
export const maintenanceSchedules = sqliteTable("maintenance_schedules", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  lockerId: integer("locker_id").notNull().references(() => lockers.id),
  type: text("type").notNull(), // routine, predictive, emergency
  priority: text("priority").notNull().default("medium"), // low, medium, high, critical
  scheduledDate: integer("scheduled_date").notNull(),
  estimatedDuration: integer("estimated_duration"), // minutes
  description: text("description"),
  assignedTo: text("assigned_to").references(() => users.id),
  status: text("status").notNull().default("scheduled"), // scheduled, in_progress, completed, cancelled
  completedAt: integer("completed_at"),
  notes: text("notes"),
  cost: real("cost"),
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  lockerIdx: index("idx_maintenance_schedule_locker").on(table.lockerId),
  statusIdx: index("idx_maintenance_schedule_status").on(table.status),
  scheduledIdx: index("idx_maintenance_schedule_date").on(table.scheduledDate),
}));

// System Health Metrics table
export const systemHealthMetrics = sqliteTable("system_health_metrics", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  metric: text("metric").notNull(), // cpu_usage, memory_usage, disk_usage, response_time, etc.
  value: real("value").notNull(),
  unit: text("unit").notNull(), // %, ms, MB, etc.
  threshold: real("threshold"), // Alert threshold
  status: text("status").notNull().default("normal"), // normal, warning, critical
  source: text("source"), // server, database, api, locker
  metadata: text("metadata"), // Additional context as JSON
  timestamp: integer("timestamp").default(Date.now()),
}, (table) => ({
  metricIdx: index("idx_health_metric").on(table.metric),
  timestampIdx: index("idx_health_timestamp").on(table.timestamp),
  statusIdx: index("idx_health_status").on(table.status),
}));

// API Usage Tracking table
export const apiUsageTracking = sqliteTable("api_usage_tracking", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  endpoint: text("endpoint").notNull(),
  method: text("method").notNull(), // GET, POST, PUT, DELETE
  userId: text("user_id").references(() => users.id),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  responseStatus: integer("response_status").notNull(),
  responseTime: integer("response_time"), // milliseconds
  requestSize: integer("request_size"), // bytes
  responseSize: integer("response_size"), // bytes
  timestamp: integer("timestamp").default(Date.now()),
}, (table) => ({
  endpointIdx: index("idx_api_usage_endpoint").on(table.endpoint),
  userIdx: index("idx_api_usage_user").on(table.userId),
  timestampIdx: index("idx_api_usage_timestamp").on(table.timestamp),
  statusIdx: index("idx_api_usage_status").on(table.responseStatus),
}));

// Notification Templates table
export const notificationTemplates = sqliteTable("notification_templates", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull().unique(),
  type: text("type").notNull(), // email, sms, push, webhook
  subject: text("subject"), // For email notifications
  template: text("template").notNull(), // Template content with placeholders
  variables: text("variables"), // JSON array of available variables
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  category: text("category").notNull(), // booking, maintenance, alert, system
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  nameIdx: index("idx_notification_template_name").on(table.name),
  typeIdx: index("idx_notification_template_type").on(table.type),
  categoryIdx: index("idx_notification_template_category").on(table.category),
}));

// Integrations table for third-party services
export const integrations = sqliteTable("integrations", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  type: text("type").notNull(), // payment, notification, analytics, etc.
  provider: text("provider").notNull(),
  status: text("status").notNull().default("pending"), // pending, active, error, inactive
  description: text("description"),
  apiEndpoint: text("api_endpoint").notNull(),
  authType: text("auth_type").notNull(), // api_key, oauth, basic, bearer
  credentials: text("credentials").notNull(), // encrypted JSON
  webhookUrl: text("webhook_url"),
  lastSync: integer("last_sync"),
  syncFrequency: integer("sync_frequency").default(60), // minutes
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  errorMessage: text("error_message"),
  metadata: text("metadata"), // JSON metadata
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(sql`(strftime('%s', 'now') * 1000)`),
  updatedAt: integer("updated_at").default(sql`(strftime('%s', 'now') * 1000)`),
}, (table) => ({
  nameIdx: index("idx_integration_name").on(table.name),
  typeIdx: index("idx_integration_type").on(table.type),
  statusIdx: index("idx_integration_status").on(table.status),
  activeIdx: index("idx_integration_active").on(table.isActive),
}));

// Rate Limit Rules table
export const rateLimitRules = sqliteTable("rate_limit_rules", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  endpoint: text("endpoint").notNull(),
  method: text("method").notNull(),
  limit: integer("limit_count").notNull(),
  window: integer("window_seconds").notNull(),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(sql`(strftime('%s', 'now') * 1000)`),
  updatedAt: integer("updated_at").default(sql`(strftime('%s', 'now') * 1000)`),
}, (table) => ({
  endpointIdx: index("idx_rate_limit_endpoint").on(table.endpoint),
  methodIdx: index("idx_rate_limit_method").on(table.method),
  activeIdx: index("idx_rate_limit_active").on(table.isActive),
}));

// API Usage Logs table (enhanced version)
export const apiUsageLogs = sqliteTable("api_usage_logs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  endpoint: text("endpoint").notNull(),
  method: text("method").notNull(),
  userId: text("user_id").references(() => users.id),
  ipAddress: text("ip_address").notNull(),
  userAgent: text("user_agent"),
  responseStatus: integer("response_status").notNull(),
  responseTime: integer("response_time").notNull(), // milliseconds
  requestSize: integer("request_size").default(0),
  responseSize: integer("response_size").default(0),
  timestamp: integer("timestamp").default(sql`(strftime('%s', 'now') * 1000)`),
}, (table) => ({
  endpointIdx: index("idx_api_logs_endpoint").on(table.endpoint),
  userIdx: index("idx_api_logs_user").on(table.userId),
  timestampIdx: index("idx_api_logs_timestamp").on(table.timestamp),
  statusIdx: index("idx_api_logs_status").on(table.responseStatus),
}));

// Bulk Operations table
export const bulkOperations = sqliteTable("bulk_operations", {
  id: text("id").primaryKey(),
  operationType: text("operation_type").notNull(),
  targetCount: integer("target_count").notNull(),
  processedCount: integer("processed_count").default(0),
  successCount: integer("success_count").default(0),
  errorCount: integer("error_count").default(0),
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  parameters: text("parameters"), // JSON parameters
  errors: text("errors"), // JSON array of errors
  startedBy: text("started_by").notNull().references(() => users.id),
  startedAt: integer("started_at").default(sql`(strftime('%s', 'now') * 1000)`),
  completedAt: integer("completed_at"),
}, (table) => ({
  typeIdx: index("idx_bulk_operation_type").on(table.operationType),
  statusIdx: index("idx_bulk_operation_status").on(table.status),
  startedByIdx: index("idx_bulk_operation_started_by").on(table.startedBy),
}));

// Workflow Automation table
export const workflowAutomations = sqliteTable("workflow_automations", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  description: text("description"),
  trigger: text("trigger").notNull(), // JSON trigger configuration
  conditions: text("conditions"), // JSON conditions array
  actions: text("actions").notNull(), // JSON actions array
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  executionCount: integer("execution_count").default(0),
  lastExecuted: integer("last_executed"),
  createdBy: text("created_by").notNull().references(() => users.id),
  createdAt: integer("created_at").default(Date.now()),
  updatedAt: integer("updated_at").default(Date.now()),
}, (table) => ({
  nameIdx: index("idx_workflow_name").on(table.name),
  activeIdx: index("idx_workflow_active").on(table.isActive),
}));

export const lockersRelations = relations(lockers, ({ one, many }) => ({
  location: one(locations, {
    fields: [lockers.locationId],
    references: [locations.id],
  }),
  bookings: many(bookings),
  activities: many(activities),
}));

export const locationsRelations = relations(locations, ({ many }) => ({
  lockers: many(lockers),
}));

export const bookingsRelations = relations(bookings, ({ one, many }) => ({
  user: one(users, {
    fields: [bookings.userId],
    references: [users.id],
  }),
  locker: one(lockers, {
    fields: [bookings.lockerId],
    references: [lockers.id],
  }),
  agent: one(users, {
    fields: [bookings.agentId],
    references: [users.id],
  }),
  tasks: many(tasks),
  chatMessages: many(chatMessages),
}));

export const tasksRelations = relations(tasks, ({ one }) => ({
  agent: one(users, {
    fields: [tasks.agentId],
    references: [users.id],
  }),
  booking: one(bookings, {
    fields: [tasks.bookingId],
    references: [bookings.id],
  }),
}));

export const activitiesRelations = relations(activities, ({ one }) => ({
  locker: one(lockers, {
    fields: [activities.lockerId],
    references: [lockers.id],
  }),
  user: one(users, {
    fields: [activities.userId],
    references: [users.id],
  }),
}));

export const chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  fromUser: one(users, {
    fields: [chatMessages.fromUserId],
    references: [users.id],
    relationName: "sentMessages",
  }),
  toUser: one(users, {
    fields: [chatMessages.toUserId],
    references: [users.id],
    relationName: "receivedMessages",
  }),
  booking: one(bookings, {
    fields: [chatMessages.bookingId],
    references: [bookings.id],
  }),
}));

export const agentBiometricsRelations = relations(agentBiometrics, ({ one }) => ({
  agent: one(users, {
    fields: [agentBiometrics.agentId],
    references: [users.id],
  }),
}));

export const lockerAccessSessionsRelations = relations(lockerAccessSessions, ({ one, many }) => ({
  booking: one(bookings, {
    fields: [lockerAccessSessions.bookingId],
    references: [bookings.id],
  }),
  user: one(users, {
    fields: [lockerAccessSessions.userId],
    references: [users.id],
    relationName: "userAccessSessions",
  }),
  agent: one(users, {
    fields: [lockerAccessSessions.agentId],
    references: [users.id],
    relationName: "agentAccessSessions",
  }),
  locker: one(lockers, {
    fields: [lockerAccessSessions.lockerId],
    references: [lockers.id],
  }),
  notifications: many(expirationNotifications),
}));

export const expirationNotificationsRelations = relations(expirationNotifications, ({ one }) => ({
  session: one(lockerAccessSessions, {
    fields: [expirationNotifications.sessionId],
    references: [lockerAccessSessions.id],
  }),
  user: one(users, {
    fields: [expirationNotifications.userId],
    references: [users.id],
    relationName: "userNotifications",
  }),
  agent: one(users, {
    fields: [expirationNotifications.agentId],
    references: [users.id],
    relationName: "agentNotifications",
  }),
}));

export const subscriptionPlansRelations = relations(subscriptionPlans, ({ many }) => ({
  subscriptionHistory: many(subscriptionHistory),
}));

export const subscriptionHistoryRelations = relations(subscriptionHistory, ({ one }) => ({
  user: one(users, {
    fields: [subscriptionHistory.userId],
    references: [users.id],
  }),
  plan: one(subscriptionPlans, {
    fields: [subscriptionHistory.planId],
    references: [subscriptionPlans.id],
  }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  createdAt: true,
  updatedAt: true,
});

export const insertLockerSchema = createInsertSchema(lockers).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertLocationSchema = createInsertSchema(locations).omit({
  id: true,
  createdAt: true,
});

export const insertBookingSchema = createInsertSchema(bookings).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertTaskSchema = createInsertSchema(tasks).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertActivitySchema = createInsertSchema(activities).omit({
  id: true,
  createdAt: true,
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).omit({
  id: true,
  createdAt: true,
});

export const insertAgentBiometricSchema = createInsertSchema(agentBiometrics).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertLockerAccessSessionSchema = createInsertSchema(lockerAccessSessions).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertExpirationNotificationSchema = createInsertSchema(expirationNotifications).omit({
  id: true,
  createdAt: true,
});

export const insertSubscriptionPlanSchema = createInsertSchema(subscriptionPlans).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertSubscriptionHistorySchema = createInsertSchema(subscriptionHistory).omit({
  id: true,
  createdAt: true,
});

// Types
export type UpsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertLocker = z.infer<typeof insertLockerSchema>;
export type Locker = typeof lockers.$inferSelect;
export type InsertLocation = z.infer<typeof insertLocationSchema>;
export type Location = typeof locations.$inferSelect;
export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type Booking = typeof bookings.$inferSelect;
export type InsertTask = z.infer<typeof insertTaskSchema>;
export type Task = typeof tasks.$inferSelect;
export type InsertActivity = z.infer<typeof insertActivitySchema>;
export type Activity = typeof activities.$inferSelect;
export type InsertChatMessage = z.infer<typeof insertChatMessageSchema>;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type InsertAgentBiometric = z.infer<typeof insertAgentBiometricSchema>;
export type AgentBiometric = typeof agentBiometrics.$inferSelect;
export type InsertLockerAccessSession = z.infer<typeof insertLockerAccessSessionSchema>;
export type LockerAccessSession = typeof lockerAccessSessions.$inferSelect;
export type InsertExpirationNotification = z.infer<typeof insertExpirationNotificationSchema>;
export type ExpirationNotification = typeof expirationNotifications.$inferSelect;
export type SubscriptionPlan = typeof subscriptionPlans.$inferSelect;
export type InsertSubscriptionPlan = z.infer<typeof insertSubscriptionPlanSchema>;
export type SubscriptionHistory = typeof subscriptionHistory.$inferSelect;
export type InsertSubscriptionHistory = z.infer<typeof insertSubscriptionHistorySchema>;

// New table types
export type SystemSetting = typeof systemSettings.$inferSelect;
export type InsertSystemSetting = typeof systemSettings.$inferInsert;
export type AuditLog = typeof auditLogs.$inferSelect;
export type InsertAuditLog = typeof auditLogs.$inferInsert;
export type AdminSession = typeof adminSessions.$inferSelect;
export type InsertAdminSession = typeof adminSessions.$inferInsert;
export type AlertRule = typeof alertRules.$inferSelect;
export type InsertAlertRule = typeof alertRules.$inferInsert;
export type ScheduledReport = typeof scheduledReports.$inferSelect;
export type InsertScheduledReport = typeof scheduledReports.$inferInsert;
export type MaintenanceSchedule = typeof maintenanceSchedules.$inferSelect;
export type InsertMaintenanceSchedule = typeof maintenanceSchedules.$inferInsert;
export type SystemHealthMetric = typeof systemHealthMetrics.$inferSelect;
export type InsertSystemHealthMetric = typeof systemHealthMetrics.$inferInsert;
export type ApiUsageTracking = typeof apiUsageTracking.$inferSelect;
export type InsertApiUsageTracking = typeof apiUsageTracking.$inferInsert;
export type NotificationTemplate = typeof notificationTemplates.$inferSelect;
export type InsertNotificationTemplate = typeof notificationTemplates.$inferInsert;
export type WorkflowAutomation = typeof workflowAutomations.$inferSelect;
export type InsertWorkflowAutomation = typeof workflowAutomations.$inferInsert;
